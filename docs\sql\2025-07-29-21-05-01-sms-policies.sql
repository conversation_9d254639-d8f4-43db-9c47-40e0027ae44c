-- SMS Notification System RLS Policies
-- Created: 2025-07-29 21:05:01
-- Description: Row Level Security policies for SMS notification tables

-- =====================================================
-- ENABLE RLS ON ALL SMS TABLES
-- =====================================================

ALTER TABLE otp_verifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE appointment_cancellation_tokens ENABLE ROW LEVEL SECURITY;
ALTER TABLE salon_sms_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE sms_delivery_log ENABLE ROW LEVEL SECURITY;
ALTER TABLE system_sms_config ENABLE ROW LEVEL SECURITY;

-- =====================================================
-- 1. OTP VERIFICATIONS POLICIES
-- =====================================================

-- Policy for salon owners/staff to manage OTP verifications for their salon
CREATE POLICY "Salon users can manage OTP verifications for their salon"
ON otp_verifications
FOR ALL
USING (
    salon_id IN (
        SELECT s.id 
        FROM salons s 
        WHERE s.owner_id = auth.uid()
        OR EXISTS (
            SELECT 1 FROM barbers b 
            WHERE b.salon_id = s.id 
            AND b.user_id = auth.uid()
        )
    )
);

-- Policy for admins to access all OTP verifications
CREATE POLICY "Admins can access all OTP verifications"
ON otp_verifications
FOR ALL
USING (is_admin());

-- Policy for public OTP verification (for unauthenticated users during booking)
-- This will be handled through RPC functions for security
CREATE POLICY "Public OTP verification through RPC only"
ON otp_verifications
FOR SELECT
USING (FALSE); -- Deny direct access, use RPC functions

-- =====================================================
-- 2. APPOINTMENT CANCELLATION TOKENS POLICIES
-- =====================================================

-- Policy for salon users to manage cancellation tokens for their salon
CREATE POLICY "Salon users can manage cancellation tokens for their salon"
ON appointment_cancellation_tokens
FOR ALL
USING (
    salon_id IN (
        SELECT s.id 
        FROM salons s 
        WHERE s.owner_id = auth.uid()
        OR EXISTS (
            SELECT 1 FROM barbers b 
            WHERE b.salon_id = s.id 
            AND b.user_id = auth.uid()
        )
    )
);

-- Policy for admins to access all cancellation tokens
CREATE POLICY "Admins can access all cancellation tokens"
ON appointment_cancellation_tokens
FOR ALL
USING (is_admin());

-- Policy for public token validation (for unauthenticated cancellation)
-- This will be handled through RPC functions for security
CREATE POLICY "Public token validation through RPC only"
ON appointment_cancellation_tokens
FOR SELECT
USING (FALSE); -- Deny direct access, use RPC functions

-- =====================================================
-- 3. SALON SMS SETTINGS POLICIES
-- =====================================================

-- Policy for salon owners to manage their SMS settings
CREATE POLICY "Salon owners can manage their SMS settings"
ON salon_sms_settings
FOR ALL
USING (
    salon_id IN (
        SELECT s.id 
        FROM salons s 
        WHERE s.owner_id = auth.uid()
    )
);

-- Policy for salon staff to view SMS settings (read-only)
CREATE POLICY "Salon staff can view SMS settings"
ON salon_sms_settings
FOR SELECT
USING (
    salon_id IN (
        SELECT s.id 
        FROM salons s 
        WHERE EXISTS (
            SELECT 1 FROM barbers b 
            WHERE b.salon_id = s.id 
            AND b.user_id = auth.uid()
        )
    )
);

-- Policy for admins to access all SMS settings
CREATE POLICY "Admins can access all SMS settings"
ON salon_sms_settings
FOR ALL
USING (is_admin());

-- =====================================================
-- 4. SMS DELIVERY LOG POLICIES
-- =====================================================

-- Policy for salon users to view SMS delivery logs for their salon
CREATE POLICY "Salon users can view SMS delivery logs for their salon"
ON sms_delivery_log
FOR SELECT
USING (
    salon_id IN (
        SELECT s.id 
        FROM salons s 
        WHERE s.owner_id = auth.uid()
        OR EXISTS (
            SELECT 1 FROM barbers b 
            WHERE b.salon_id = s.id 
            AND b.user_id = auth.uid()
        )
    )
);

-- Policy for system to insert SMS delivery logs
CREATE POLICY "System can insert SMS delivery logs"
ON sms_delivery_log
FOR INSERT
WITH CHECK (TRUE); -- Allow system to log all SMS attempts

-- Policy for system to update SMS delivery logs (for status updates)
CREATE POLICY "System can update SMS delivery logs"
ON sms_delivery_log
FOR UPDATE
USING (TRUE); -- Allow system to update delivery status

-- Policy for admins to access all SMS delivery logs
CREATE POLICY "Admins can access all SMS delivery logs"
ON sms_delivery_log
FOR ALL
USING (is_admin());

-- =====================================================
-- 5. SYSTEM SMS CONFIG POLICIES
-- =====================================================

-- Policy for admins to manage system SMS configuration
CREATE POLICY "Admins can manage system SMS configuration"
ON system_sms_config
FOR ALL
USING (is_admin());

-- Policy for authenticated users to view basic system SMS config (for rate limits, etc.)
CREATE POLICY "Authenticated users can view basic system SMS config"
ON system_sms_config
FOR SELECT
USING (
    auth.uid() IS NOT NULL
    AND NOT emergency_disable -- Hide config during emergency
);

-- =====================================================
-- RPC FUNCTIONS FOR SECURE PUBLIC ACCESS
-- =====================================================

-- Function to verify OTP for unauthenticated users
CREATE OR REPLACE FUNCTION verify_otp_public(
    p_salon_id UUID,
    p_phone_number TEXT,
    p_otp_code TEXT,
    p_ip_address INET DEFAULT NULL,
    p_user_agent TEXT DEFAULT NULL
)
RETURNS JSONB
SECURITY DEFINER
SET search_path = public
LANGUAGE plpgsql
AS $$
DECLARE
    v_otp_record otp_verifications%ROWTYPE;
    v_result JSONB;
BEGIN
    -- Find the OTP record
    SELECT * INTO v_otp_record
    FROM otp_verifications
    WHERE salon_id = p_salon_id
      AND phone_number = p_phone_number
      AND otp_code = p_otp_code
      AND NOT is_verified
      AND expires_at > NOW()
      AND attempts < max_attempts
    ORDER BY created_at DESC
    LIMIT 1;
    
    -- Check if OTP exists and is valid
    IF NOT FOUND THEN
        -- Increment attempts for any matching unverified OTP
        UPDATE otp_verifications
        SET attempts = attempts + 1,
            updated_at = NOW()
        WHERE salon_id = p_salon_id
          AND phone_number = p_phone_number
          AND NOT is_verified
          AND expires_at > NOW();
          
        RETURN jsonb_build_object(
            'success', false,
            'error', 'invalid_otp',
            'message', 'Geçersiz doğrulama kodu'
        );
    END IF;
    
    -- Mark OTP as verified
    UPDATE otp_verifications
    SET is_verified = true,
        verified_at = NOW(),
        updated_at = NOW()
    WHERE id = v_otp_record.id;
    
    -- Return success
    RETURN jsonb_build_object(
        'success', true,
        'otp_id', v_otp_record.id,
        'context_data', v_otp_record.context_data
    );
    
EXCEPTION
    WHEN OTHERS THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', 'system_error',
            'message', 'Sistem hatası oluştu'
        );
END;
$$;

-- Function to validate and use cancellation token
CREATE OR REPLACE FUNCTION use_cancellation_token_public(
    p_token TEXT,
    p_ip_address INET DEFAULT NULL,
    p_user_agent TEXT DEFAULT NULL
)
RETURNS JSONB
SECURITY DEFINER
SET search_path = public
LANGUAGE plpgsql
AS $$
DECLARE
    v_token_record appointment_cancellation_tokens%ROWTYPE;
    v_appointment_record appointments%ROWTYPE;
    v_result JSONB;
BEGIN
    -- Find the token record
    SELECT * INTO v_token_record
    FROM appointment_cancellation_tokens
    WHERE token = p_token
      AND NOT is_used
      AND expires_at > NOW();
    
    -- Check if token exists and is valid
    IF NOT FOUND THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', 'invalid_token',
            'message', 'Geçersiz veya süresi dolmuş iptal bağlantısı'
        );
    END IF;
    
    -- Get appointment details
    SELECT * INTO v_appointment_record
    FROM appointments
    WHERE id = v_token_record.appointment_id;
    
    -- Check if appointment exists and can be cancelled
    IF NOT FOUND THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', 'appointment_not_found',
            'message', 'Randevu bulunamadı'
        );
    END IF;
    
    -- Check if appointment is already cancelled
    IF v_appointment_record.status = 'cancelled' THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', 'already_cancelled',
            'message', 'Bu randevu zaten iptal edilmiş'
        );
    END IF;
    
    -- Mark token as used
    UPDATE appointment_cancellation_tokens
    SET is_used = true,
        used_at = NOW(),
        used_ip_address = p_ip_address,
        used_user_agent = p_user_agent,
        updated_at = NOW()
    WHERE id = v_token_record.id;
    
    -- Cancel the appointment
    UPDATE appointments
    SET status = 'cancelled',
        updated_at = NOW()
    WHERE id = v_token_record.appointment_id;
    
    -- Return success with appointment details
    RETURN jsonb_build_object(
        'success', true,
        'appointment_id', v_appointment_record.id,
        'salon_id', v_appointment_record.salon_id,
        'appointment_date', v_appointment_record.date,
        'appointment_time', v_appointment_record.start_time,
        'customer_name', v_appointment_record.fullname,
        'customer_phone', v_appointment_record.phonenumber
    );
    
EXCEPTION
    WHEN OTHERS THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', 'system_error',
            'message', 'Sistem hatası oluştu'
        );
END;
$$;

-- Function to generate OTP for public booking
CREATE OR REPLACE FUNCTION generate_otp_public(
    p_salon_id UUID,
    p_phone_number TEXT,
    p_purpose TEXT DEFAULT 'booking_verification',
    p_context_data JSONB DEFAULT NULL,
    p_ip_address INET DEFAULT NULL,
    p_user_agent TEXT DEFAULT NULL
)
RETURNS JSONB
SECURITY DEFINER
SET search_path = public
LANGUAGE plpgsql
AS $$
DECLARE
    v_otp_code TEXT;
    v_expires_at TIMESTAMP WITH TIME ZONE;
    v_otp_id UUID;
    v_config system_sms_config%ROWTYPE;
BEGIN
    -- Get system configuration
    SELECT * INTO v_config FROM system_sms_config LIMIT 1;
    
    -- Check if SMS is enabled
    IF NOT v_config.is_sms_enabled OR v_config.emergency_disable THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', 'sms_disabled',
            'message', 'SMS servisi şu anda kullanılamıyor'
        );
    END IF;
    
    -- Generate 6-digit OTP
    v_otp_code := LPAD(FLOOR(RANDOM() * 1000000)::TEXT, 6, '0');
    
    -- Set expiration time
    v_expires_at := NOW() + INTERVAL '1 minute' * v_config.otp_expiry_minutes;
    
    -- Invalidate any existing unverified OTPs for this phone/salon
    UPDATE otp_verifications
    SET is_verified = false,
        updated_at = NOW()
    WHERE salon_id = p_salon_id
      AND phone_number = p_phone_number
      AND NOT is_verified
      AND expires_at > NOW();
    
    -- Insert new OTP
    INSERT INTO otp_verifications (
        salon_id,
        phone_number,
        otp_code,
        purpose,
        expires_at,
        context_data,
        ip_address,
        user_agent
    ) VALUES (
        p_salon_id,
        p_phone_number,
        v_otp_code,
        p_purpose,
        v_expires_at,
        p_context_data,
        p_ip_address,
        p_user_agent
    ) RETURNING id INTO v_otp_id;
    
    -- Return success with OTP details
    RETURN jsonb_build_object(
        'success', true,
        'otp_id', v_otp_id,
        'otp_code', v_otp_code,
        'expires_at', v_expires_at,
        'expires_in_minutes', v_config.otp_expiry_minutes
    );
    
EXCEPTION
    WHEN OTHERS THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', 'system_error',
            'message', 'Sistem hatası oluştu'
        );
END;
$$;

-- Grant execute permissions on RPC functions
GRANT EXECUTE ON FUNCTION verify_otp_public TO anon, authenticated;
GRANT EXECUTE ON FUNCTION use_cancellation_token_public TO anon, authenticated;
GRANT EXECUTE ON FUNCTION generate_otp_public TO anon, authenticated;
