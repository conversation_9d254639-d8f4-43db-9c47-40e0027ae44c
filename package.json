{"name": "kuafor", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test:subscription": "node test/run-subscription-tests.js"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@hookform/resolvers": "^5.0.1", "@netgsm/sms": "^1.1.10", "@radix-ui/react-alert-dialog": "^1.1.13", "@radix-ui/react-avatar": "^1.1.9", "@radix-ui/react-checkbox": "^1.3.1", "@radix-ui/react-collapsible": "^1.1.10", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-popover": "^1.1.13", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.6", "@radix-ui/react-scroll-area": "^1.2.8", "@radix-ui/react-select": "^2.2.4", "@radix-ui/react-separator": "^1.1.6", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-switch": "^1.2.4", "@radix-ui/react-tabs": "^1.1.11", "@radix-ui/react-tooltip": "^1.2.6", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.49.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "crypto": "^1.0.1", "date-fns": "^3.6.0", "dotenv": "^16.5.0", "framer-motion": "^12.10.5", "lucide-react": "^0.509.0", "next": "15.3.2", "next-themes": "^0.4.6", "react": "^19.0.0", "react-day-picker": "^8.10.1", "react-dom": "^19.0.0", "react-hook-form": "^7.56.3", "react-responsive": "^10.0.1", "react-zoom-pan-pinch": "^3.7.0", "recharts": "^2.15.3", "sonner": "^2.0.3", "tailwind-merge": "^3.2.0", "zod": "^3.24.4"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@testing-library/react-hooks": "^8.0.1", "@types/chai": "^4.3.11", "@types/jest": "^29.5.12", "@types/jsdom": "^21.1.6", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/sinon": "^17.0.3", "chai": "^4.3.10", "eslint": "^9", "eslint-config-next": "15.3.2", "jest": "^29.7.0", "jsdom": "^24.0.0", "sinon": "^17.0.1", "tailwindcss": "^4", "tw-animate-css": "^1.2.9", "typescript": "^5"}}