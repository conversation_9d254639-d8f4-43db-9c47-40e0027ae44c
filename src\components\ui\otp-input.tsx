/**
 * OTP Input Component
 * A user-friendly OTP input component with auto-focus and validation
 */

import React, { useState, useRef, useEffect, KeyboardEvent, ClipboardEvent } from 'react';
import { cn } from '@/lib/utils';

interface OTPInputProps {
  length?: number;
  value?: string;
  onChange?: (value: string) => void;
  onComplete?: (value: string) => void;
  disabled?: boolean;
  error?: boolean;
  className?: string;
  placeholder?: string;
  autoFocus?: boolean;
}

export function OTPInput({
  length = 6,
  value = '',
  onChange,
  onComplete,
  disabled = false,
  error = false,
  className,
  placeholder = '0',
  autoFocus = false
}: OTPInputProps) {
  const [otp, setOtp] = useState<string[]>(new Array(length).fill(''));
  const inputRefs = useRef<(HTMLInputElement | null)[]>(new Array(length).fill(null));

  // Update internal state when value prop changes
  useEffect(() => {
    if (value !== otp.join('')) {
      const newOtp = value.split('').slice(0, length);
      while (newOtp.length < length) {
        newOtp.push('');
      }
      setOtp(newOtp);
    }
  }, [value, length]);

  // Auto-focus first input on mount
  useEffect(() => {
    if (autoFocus && inputRefs.current[0]) {
      inputRefs.current[0].focus();
    }
  }, [autoFocus]);

  // Handle input change
  const handleChange = (index: number, digit: string) => {
    // Only allow single digits
    if (digit.length > 1) {
      digit = digit.slice(-1);
    }

    // Only allow numeric input
    if (digit && !/^\d$/.test(digit)) {
      return;
    }

    const newOtp = [...otp];
    newOtp[index] = digit;
    setOtp(newOtp);

    const otpValue = newOtp.join('');
    onChange?.(otpValue);

    // Auto-focus next input
    if (digit && index < length - 1) {
      inputRefs.current[index + 1]?.focus();
    }

    // Call onComplete when all digits are filled
    if (otpValue.length === length && !otpValue.includes('')) {
      onComplete?.(otpValue);
    }
  };

  // Handle key down events
  const handleKeyDown = (index: number, e: KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Backspace') {
      e.preventDefault();
      
      if (otp[index]) {
        // Clear current input
        handleChange(index, '');
      } else if (index > 0) {
        // Move to previous input and clear it
        inputRefs.current[index - 1]?.focus();
        handleChange(index - 1, '');
      }
    } else if (e.key === 'ArrowLeft' && index > 0) {
      e.preventDefault();
      inputRefs.current[index - 1]?.focus();
    } else if (e.key === 'ArrowRight' && index < length - 1) {
      e.preventDefault();
      inputRefs.current[index + 1]?.focus();
    } else if (e.key === 'Delete') {
      e.preventDefault();
      handleChange(index, '');
    } else if (e.key === 'Home') {
      e.preventDefault();
      inputRefs.current[0]?.focus();
    } else if (e.key === 'End') {
      e.preventDefault();
      inputRefs.current[length - 1]?.focus();
    }
  };

  // Handle paste events
  const handlePaste = (e: ClipboardEvent<HTMLInputElement>) => {
    e.preventDefault();
    
    const pastedData = e.clipboardData.getData('text/plain');
    const digits = pastedData.replace(/\D/g, '').slice(0, length);
    
    if (digits) {
      const newOtp = new Array(length).fill('');
      for (let i = 0; i < digits.length && i < length; i++) {
        newOtp[i] = digits[i];
      }
      
      setOtp(newOtp);
      onChange?.(newOtp.join(''));
      
      // Focus the next empty input or the last input
      const nextEmptyIndex = newOtp.findIndex(digit => digit === '');
      const focusIndex = nextEmptyIndex !== -1 ? nextEmptyIndex : length - 1;
      inputRefs.current[focusIndex]?.focus();
      
      // Call onComplete if all digits are filled
      const otpValue = newOtp.join('');
      if (otpValue.length === length && !otpValue.includes('')) {
        onComplete?.(otpValue);
      }
    }
  };

  // Handle focus events
  const handleFocus = (index: number) => {
    // Select all text when focusing
    inputRefs.current[index]?.select();
  };

  return (
    <div className={cn('flex gap-2 justify-center', className)}>
      {otp.map((digit, index) => (
        <input
          key={index}
          ref={(el) => (inputRefs.current[index] = el)}
          type="text"
          inputMode="numeric"
          pattern="\d*"
          maxLength={1}
          value={digit}
          onChange={(e) => handleChange(index, e.target.value)}
          onKeyDown={(e) => handleKeyDown(index, e)}
          onPaste={handlePaste}
          onFocus={() => handleFocus(index)}
          disabled={disabled}
          placeholder={placeholder}
          className={cn(
            'w-12 h-12 text-center text-lg font-semibold border rounded-md',
            'focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent',
            'transition-all duration-200',
            {
              'border-red-500 focus:ring-red-500': error,
              'border-gray-300 hover:border-gray-400': !error && !disabled,
              'bg-gray-100 text-gray-500 cursor-not-allowed': disabled,
              'bg-white': !disabled
            }
          )}
          aria-label={`OTP digit ${index + 1}`}
        />
      ))}
    </div>
  );
}

// OTP Input with Label and Error Message
interface OTPInputWithLabelProps extends OTPInputProps {
  label?: string;
  errorMessage?: string;
  helperText?: string;
  required?: boolean;
}

export function OTPInputWithLabel({
  label,
  errorMessage,
  helperText,
  required = false,
  error,
  className,
  ...otpProps
}: OTPInputWithLabelProps) {
  const hasError = error || !!errorMessage;

  return (
    <div className={cn('space-y-2', className)}>
      {label && (
        <label className="block text-sm font-medium text-gray-700">
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </label>
      )}
      
      <OTPInput {...otpProps} error={hasError} />
      
      {errorMessage && (
        <p className="text-sm text-red-600" role="alert">
          {errorMessage}
        </p>
      )}
      
      {helperText && !errorMessage && (
        <p className="text-sm text-gray-500">
          {helperText}
        </p>
      )}
    </div>
  );
}

// OTP Input with Timer
interface OTPInputWithTimerProps extends OTPInputWithLabelProps {
  expiryTime?: Date;
  onExpired?: () => void;
  onResend?: () => void;
  resendDisabled?: boolean;
  resendText?: string;
  expiredText?: string;
}

export function OTPInputWithTimer({
  expiryTime,
  onExpired,
  onResend,
  resendDisabled = false,
  resendText = 'Yeniden Gönder',
  expiredText = 'Kod süresi doldu',
  helperText,
  ...props
}: OTPInputWithTimerProps) {
  const [timeLeft, setTimeLeft] = useState<number>(0);
  const [isExpired, setIsExpired] = useState(false);

  useEffect(() => {
    if (!expiryTime) return;

    const updateTimer = () => {
      const now = new Date().getTime();
      const expiry = new Date(expiryTime).getTime();
      const difference = expiry - now;

      if (difference > 0) {
        setTimeLeft(Math.ceil(difference / 1000));
        setIsExpired(false);
      } else {
        setTimeLeft(0);
        setIsExpired(true);
        onExpired?.();
      }
    };

    updateTimer();
    const interval = setInterval(updateTimer, 1000);

    return () => clearInterval(interval);
  }, [expiryTime, onExpired]);

  const formatTime = (seconds: number): string => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const timerText = isExpired 
    ? expiredText
    : timeLeft > 0 
      ? `Kalan süre: ${formatTime(timeLeft)}`
      : helperText;

  return (
    <div className="space-y-3">
      <OTPInputWithLabel
        {...props}
        helperText={timerText}
        error={props.error || isExpired}
        disabled={props.disabled || isExpired}
      />
      
      {(isExpired || timeLeft === 0) && onResend && (
        <div className="text-center">
          <button
            type="button"
            onClick={onResend}
            disabled={resendDisabled}
            className={cn(
              'text-sm font-medium underline',
              {
                'text-primary hover:text-primary/80 cursor-pointer': !resendDisabled,
                'text-gray-400 cursor-not-allowed': resendDisabled
              }
            )}
          >
            {resendText}
          </button>
        </div>
      )}
    </div>
  );
}

export default OTPInput;
