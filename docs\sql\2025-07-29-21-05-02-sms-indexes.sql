-- SMS Notification System Database Indexes
-- Created: 2025-07-29 21:05:02
-- Description: Performance indexes for SMS notification tables

-- =====================================================
-- 1. OTP VERIFICATIONS INDEXES
-- =====================================================

-- Primary lookup index for OTP verification
CREATE INDEX idx_otp_verifications_lookup 
ON otp_verifications (salon_id, phone_number, otp_code, is_verified, expires_at);

-- Index for finding active OTPs by salon and phone
CREATE INDEX idx_otp_verifications_active 
ON otp_verifications (salon_id, phone_number, is_verified, expires_at) 
WHERE NOT is_verified AND expires_at > NOW();

-- Index for cleanup of expired OTPs
CREATE INDEX idx_otp_verifications_expired 
ON otp_verifications (expires_at) 
WHERE NOT is_verified;

-- Index for security monitoring by IP
CREATE INDEX idx_otp_verifications_ip_security 
ON otp_verifications (ip_address, created_at);

-- Index for purpose-based queries
CREATE INDEX idx_otp_verifications_purpose 
ON otp_verifications (purpose, created_at);

-- Index for salon-specific OTP management
CREATE INDEX idx_otp_verifications_salon_management 
ON otp_verifications (salon_id, created_at DESC);

-- =====================================================
-- 2. APPOINTMENT CANCELLATION TOKENS INDEXES
-- =====================================================

-- Primary lookup index for token validation
CREATE INDEX idx_cancellation_tokens_lookup 
ON appointment_cancellation_tokens (token, is_used, expires_at);

-- Index for appointment-specific tokens
CREATE INDEX idx_cancellation_tokens_appointment 
ON appointment_cancellation_tokens (appointment_id, is_used);

-- Index for salon-specific token management
CREATE INDEX idx_cancellation_tokens_salon 
ON appointment_cancellation_tokens (salon_id, created_at DESC);

-- Index for cleanup of expired tokens
CREATE INDEX idx_cancellation_tokens_expired 
ON appointment_cancellation_tokens (expires_at) 
WHERE NOT is_used;

-- Index for security monitoring
CREATE INDEX idx_cancellation_tokens_security 
ON appointment_cancellation_tokens (used_ip_address, used_at) 
WHERE is_used;

-- Index for active tokens
CREATE INDEX idx_cancellation_tokens_active 
ON appointment_cancellation_tokens (salon_id, expires_at) 
WHERE NOT is_used AND expires_at > NOW();

-- =====================================================
-- 3. SALON SMS SETTINGS INDEXES
-- =====================================================

-- Primary lookup by salon_id (already unique, but for performance)
CREATE INDEX idx_salon_sms_settings_salon 
ON salon_sms_settings (salon_id);

-- Index for enabled salons
CREATE INDEX idx_salon_sms_settings_enabled 
ON salon_sms_settings (is_enabled, salon_id) 
WHERE is_enabled;

-- Index for salons using system default
CREATE INDEX idx_salon_sms_settings_system_default 
ON salon_sms_settings (use_system_default, salon_id) 
WHERE use_system_default;

-- Index for balance monitoring
CREATE INDEX idx_salon_sms_settings_balance 
ON salon_sms_settings (current_balance, last_balance_check) 
WHERE current_balance IS NOT NULL;

-- Index for rate limit resets
CREATE INDEX idx_salon_sms_settings_rate_limits 
ON salon_sms_settings (last_daily_reset, last_hourly_reset);

-- =====================================================
-- 4. SMS DELIVERY LOG INDEXES
-- =====================================================

-- Primary lookup for salon SMS logs
CREATE INDEX idx_sms_delivery_log_salon 
ON sms_delivery_log (salon_id, created_at DESC);

-- Index for delivery status monitoring
CREATE INDEX idx_sms_delivery_log_status 
ON sms_delivery_log (delivery_status, created_at DESC);

-- Index for failed SMS tracking
CREATE INDEX idx_sms_delivery_log_failed 
ON sms_delivery_log (salon_id, delivery_status, created_at DESC) 
WHERE delivery_status = 'failed';

-- Index for retry management
CREATE INDEX idx_sms_delivery_log_retry 
ON sms_delivery_log (delivery_status, retry_count, max_retries) 
WHERE delivery_status = 'failed' AND retry_count < max_retries;

-- Index for NetGSM job tracking
CREATE INDEX idx_sms_delivery_log_netgsm_job 
ON sms_delivery_log (netgsm_job_id) 
WHERE netgsm_job_id IS NOT NULL;

-- Index for appointment-related SMS
CREATE INDEX idx_sms_delivery_log_appointment 
ON sms_delivery_log (appointment_id, message_type, created_at DESC) 
WHERE appointment_id IS NOT NULL;

-- Index for OTP-related SMS
CREATE INDEX idx_sms_delivery_log_otp 
ON sms_delivery_log (otp_verification_id, message_type, created_at DESC) 
WHERE otp_verification_id IS NOT NULL;

-- Index for phone number tracking
CREATE INDEX idx_sms_delivery_log_phone 
ON sms_delivery_log (recipient_phone, created_at DESC);

-- Index for message type analysis
CREATE INDEX idx_sms_delivery_log_message_type 
ON sms_delivery_log (message_type, salon_id, created_at DESC);

-- Index for security monitoring
CREATE INDEX idx_sms_delivery_log_security 
ON sms_delivery_log (ip_address, created_at DESC) 
WHERE ip_address IS NOT NULL;

-- =====================================================
-- 5. SYSTEM SMS CONFIG INDEXES
-- =====================================================

-- Simple index for system config (usually single row)
CREATE INDEX idx_system_sms_config_active 
ON system_sms_config (is_sms_enabled, emergency_disable);

-- =====================================================
-- 6. COMPOSITE INDEXES FOR COMPLEX QUERIES
-- =====================================================

-- Index for OTP verification with rate limiting
CREATE INDEX idx_otp_verifications_rate_limit 
ON otp_verifications (phone_number, created_at DESC, attempts);

-- Index for salon SMS usage tracking
CREATE INDEX idx_sms_delivery_log_usage_tracking 
ON sms_delivery_log (salon_id, DATE(created_at), delivery_status);

-- Index for appointment cancellation flow
CREATE INDEX idx_cancellation_flow 
ON appointment_cancellation_tokens (appointment_id, salon_id, is_used, expires_at);

-- Index for SMS settings with balance
CREATE INDEX idx_sms_settings_balance_check 
ON salon_sms_settings (salon_id, is_enabled, current_balance, last_balance_check);

-- =====================================================
-- 7. PARTIAL INDEXES FOR SPECIFIC USE CASES
-- =====================================================

-- Index for pending SMS deliveries
CREATE INDEX idx_sms_delivery_pending 
ON sms_delivery_log (salon_id, created_at) 
WHERE delivery_status = 'pending';

-- Index for recent OTP attempts (last 24 hours)
CREATE INDEX idx_otp_recent_attempts 
ON otp_verifications (phone_number, salon_id, attempts, created_at) 
WHERE created_at > NOW() - INTERVAL '24 hours';

-- Index for active cancellation tokens (not expired, not used)
CREATE INDEX idx_active_cancellation_tokens 
ON appointment_cancellation_tokens (salon_id, appointment_id, created_at) 
WHERE NOT is_used AND expires_at > NOW();

-- =====================================================
-- 8. FUNCTIONAL INDEXES
-- =====================================================

-- Index for case-insensitive phone number searches
CREATE INDEX idx_otp_verifications_phone_lower 
ON otp_verifications (LOWER(phone_number), salon_id);

CREATE INDEX idx_sms_delivery_log_phone_lower 
ON sms_delivery_log (LOWER(recipient_phone), salon_id);

-- Index for date-based partitioning simulation
CREATE INDEX idx_sms_delivery_log_date_partition 
ON sms_delivery_log (DATE(created_at), salon_id, delivery_status);

CREATE INDEX idx_otp_verifications_date_partition 
ON otp_verifications (DATE(created_at), salon_id, is_verified);

-- =====================================================
-- 9. COVERING INDEXES FOR COMMON QUERIES
-- =====================================================

-- Covering index for OTP verification lookup
CREATE INDEX idx_otp_verification_covering 
ON otp_verifications (salon_id, phone_number, otp_code) 
INCLUDE (is_verified, expires_at, attempts, max_attempts, context_data);

-- Covering index for token validation
CREATE INDEX idx_token_validation_covering 
ON appointment_cancellation_tokens (token) 
INCLUDE (appointment_id, salon_id, is_used, expires_at);

-- Covering index for SMS settings lookup
CREATE INDEX idx_sms_settings_covering 
ON salon_sms_settings (salon_id) 
INCLUDE (is_enabled, use_system_default, netgsm_username_encrypted, netgsm_password_encrypted, netgsm_header);

-- =====================================================
-- 10. COMMENTS FOR DOCUMENTATION
-- =====================================================

COMMENT ON INDEX idx_otp_verifications_lookup IS 'Primary lookup index for OTP verification queries';
COMMENT ON INDEX idx_cancellation_tokens_lookup IS 'Primary lookup index for cancellation token validation';
COMMENT ON INDEX idx_salon_sms_settings_salon IS 'Primary lookup index for salon SMS settings';
COMMENT ON INDEX idx_sms_delivery_log_salon IS 'Primary index for salon-specific SMS delivery logs';
COMMENT ON INDEX idx_otp_verifications_active IS 'Optimized index for finding active (unverified, non-expired) OTPs';
COMMENT ON INDEX idx_cancellation_tokens_active IS 'Optimized index for finding active (unused, non-expired) tokens';
COMMENT ON INDEX idx_sms_delivery_log_retry IS 'Index for identifying SMS deliveries that can be retried';

-- =====================================================
-- 11. MAINTENANCE QUERIES FOR INDEX MONITORING
-- =====================================================

-- Query to monitor index usage (run periodically)
/*
SELECT 
    schemaname,
    tablename,
    indexname,
    idx_tup_read,
    idx_tup_fetch,
    idx_scan
FROM pg_stat_user_indexes 
WHERE schemaname = 'public' 
  AND tablename IN (
    'otp_verifications',
    'appointment_cancellation_tokens',
    'salon_sms_settings',
    'sms_delivery_log',
    'system_sms_config'
  )
ORDER BY tablename, idx_scan DESC;
*/

-- Query to monitor table sizes and index effectiveness
/*
SELECT 
    tablename,
    pg_size_pretty(pg_total_relation_size(tablename::regclass)) as total_size,
    pg_size_pretty(pg_relation_size(tablename::regclass)) as table_size,
    pg_size_pretty(pg_total_relation_size(tablename::regclass) - pg_relation_size(tablename::regclass)) as index_size
FROM pg_tables 
WHERE schemaname = 'public' 
  AND tablename IN (
    'otp_verifications',
    'appointment_cancellation_tokens',
    'salon_sms_settings',
    'sms_delivery_log',
    'system_sms_config'
  );
*/
