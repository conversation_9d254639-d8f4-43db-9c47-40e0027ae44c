/**
 * NetGSM SMS Service Wrapper
 * Provides a clean interface for sending SMS messages using the NetGSM API
 */

import Netgsm from '@netgsm/sms';
import { encryptText, decryptText } from '@/lib/utils/encryption';

// NetGSM API Response Types
export interface NetGSMSendResponse {
  success: boolean;
  jobid?: string;
  error?: string;
  message?: string;
}

export interface NetGSMBalanceResponse {
  success: boolean;
  balance?: number;
  error?: string;
}

export interface NetGSMReportResponse {
  success: boolean;
  reports?: Array<{
    jobid: string;
    status: string;
    phone: string;
    message: string;
    sentDate: string;
  }>;
  error?: string;
}

// SMS Configuration Interface
export interface SMSConfig {
  username: string;
  password: string;
  header: string;
  appname?: string;
}

// SMS Message Interface
export interface SMSMessage {
  phone: string;
  message: string;
}

// SMS Send Options
export interface SMSSendOptions {
  encoding?: 'TR' | 'ASCII';
  startdate?: string; // Format: ddMMyyyyHHmm
  stopdate?: string;  // Format: ddMMyyyyHHmm
}

/**
 * NetGSM SMS Service Class
 * Handles SMS sending, balance checking, and delivery reports
 */
export class NetGSMService {
  private netgsm: Netgsm;
  private config: SMSConfig;

  constructor(config: SMSConfig) {
    this.config = config;
    this.netgsm = new Netgsm({
      username: config.username,
      password: config.password,
      appname: config.appname || 'SalonFlow'
    });
  }

  /**
   * Send a single SMS message
   */
  async sendSMS(
    phone: string, 
    message: string, 
    options: SMSSendOptions = {}
  ): Promise<NetGSMSendResponse> {
    try {
      // Validate phone number (Turkish format: 5XXXXXXXXX)
      if (!this.isValidTurkishPhone(phone)) {
        return {
          success: false,
          error: 'invalid_phone',
          message: 'Geçersiz telefon numarası formatı'
        };
      }

      // Prepare SMS data
      const smsData = {
        msgheader: this.config.header,
        encoding: options.encoding || 'TR',
        messages: [{ msg: message, no: phone }],
        ...(options.startdate && { startdate: options.startdate }),
        ...(options.stopdate && { stopdate: options.stopdate })
      };

      // Send SMS via NetGSM API
      const response = await this.netgsm.sendRestSms(smsData);

      // Handle NetGSM response
      if (response && response.jobid) {
        return {
          success: true,
          jobid: response.jobid
        };
      } else {
        return {
          success: false,
          error: 'netgsm_error',
          message: 'NetGSM API hatası'
        };
      }

    } catch (error) {
      console.error('NetGSM SMS send error:', error);
      return {
        success: false,
        error: 'system_error',
        message: 'SMS gönderimi sırasında sistem hatası oluştu'
      };
    }
  }

  /**
   * Send multiple SMS messages (bulk)
   */
  async sendBulkSMS(
    messages: SMSMessage[], 
    options: SMSSendOptions = {}
  ): Promise<NetGSMSendResponse> {
    try {
      // Validate all phone numbers
      for (const msg of messages) {
        if (!this.isValidTurkishPhone(msg.phone)) {
          return {
            success: false,
            error: 'invalid_phone',
            message: `Geçersiz telefon numarası: ${msg.phone}`
          };
        }
      }

      // Prepare bulk SMS data
      const smsData = {
        msgheader: this.config.header,
        encoding: options.encoding || 'TR',
        messages: messages.map(msg => ({ msg: msg.message, no: msg.phone })),
        ...(options.startdate && { startdate: options.startdate }),
        ...(options.stopdate && { stopdate: options.stopdate })
      };

      // Send bulk SMS via NetGSM API
      const response = await this.netgsm.sendRestSms(smsData);

      // Handle NetGSM response
      if (response && response.jobid) {
        return {
          success: true,
          jobid: response.jobid
        };
      } else {
        return {
          success: false,
          error: 'netgsm_error',
          message: 'NetGSM API hatası'
        };
      }

    } catch (error) {
      console.error('NetGSM bulk SMS send error:', error);
      return {
        success: false,
        error: 'system_error',
        message: 'Toplu SMS gönderimi sırasında sistem hatası oluştu'
      };
    }
  }

  /**
   * Check SMS delivery report
   */
  async getDeliveryReport(jobIds: string[]): Promise<NetGSMReportResponse> {
    try {
      const response = await this.netgsm.getReport({ bulkIds: jobIds });
      
      if (response) {
        return {
          success: true,
          reports: response.map((report: any) => ({
            jobid: report.jobid || '',
            status: report.status || 'unknown',
            phone: report.phone || '',
            message: report.message || '',
            sentDate: report.sentDate || ''
          }))
        };
      } else {
        return {
          success: false,
          error: 'no_report',
          message: 'Rapor bulunamadı'
        };
      }

    } catch (error) {
      console.error('NetGSM report error:', error);
      return {
        success: false,
        error: 'system_error',
        message: 'Rapor alınırken sistem hatası oluştu'
      };
    }
  }

  /**
   * Check account balance
   */
  async getBalance(): Promise<NetGSMBalanceResponse> {
    try {
      const response = await this.netgsm.getBalance({ type: 2 }); // Credit info
      
      if (response && typeof response.balance === 'number') {
        return {
          success: true,
          balance: response.balance
        };
      } else {
        return {
          success: false,
          error: 'balance_error',
          message: 'Bakiye bilgisi alınamadı'
        };
      }

    } catch (error) {
      console.error('NetGSM balance error:', error);
      return {
        success: false,
        error: 'system_error',
        message: 'Bakiye sorgulanırken sistem hatası oluştu'
      };
    }
  }

  /**
   * Get available SMS headers
   */
  async getHeaders(): Promise<{ success: boolean; headers?: string[]; error?: string }> {
    try {
      const response = await this.netgsm.getHeaders();
      
      if (response && Array.isArray(response)) {
        return {
          success: true,
          headers: response
        };
      } else {
        return {
          success: false,
          error: 'headers_error',
          message: 'Başlık bilgileri alınamadı'
        };
      }

    } catch (error) {
      console.error('NetGSM headers error:', error);
      return {
        success: false,
        error: 'system_error',
        message: 'Başlık bilgileri sorgulanırken sistem hatası oluştu'
      };
    }
  }

  /**
   * Cancel scheduled SMS
   */
  async cancelSMS(jobId: string): Promise<{ success: boolean; error?: string }> {
    try {
      const response = await this.netgsm.cancelSms({ jobid: jobId });
      
      if (response) {
        return { success: true };
      } else {
        return {
          success: false,
          error: 'cancel_error',
          message: 'SMS iptal edilemedi'
        };
      }

    } catch (error) {
      console.error('NetGSM cancel error:', error);
      return {
        success: false,
        error: 'system_error',
        message: 'SMS iptal edilirken sistem hatası oluştu'
      };
    }
  }

  /**
   * Validate Turkish phone number format
   * Expected format: 5XXXXXXXXX (10 digits starting with 5)
   */
  private isValidTurkishPhone(phone: string): boolean {
    const phoneRegex = /^5[0-9]{9}$/;
    return phoneRegex.test(phone);
  }

  /**
   * Format phone number to Turkish format
   * Removes country code and formatting
   */
  static formatTurkishPhone(phone: string): string {
    // Remove all non-digit characters
    const cleaned = phone.replace(/\D/g, '');
    
    // Handle different formats
    if (cleaned.startsWith('905')) {
      // +90 5XX XXX XX XX -> 5XXXXXXXXX
      return cleaned.substring(2);
    } else if (cleaned.startsWith('90') && cleaned.length === 12) {
      // 90 5XX XXX XX XX -> 5XXXXXXXXX
      return cleaned.substring(2);
    } else if (cleaned.startsWith('0') && cleaned.length === 11) {
      // 0 5XX XXX XX XX -> 5XXXXXXXXX
      return cleaned.substring(1);
    } else if (cleaned.startsWith('5') && cleaned.length === 10) {
      // 5XX XXX XX XX -> 5XXXXXXXXX (already correct)
      return cleaned;
    }
    
    // Return as-is if format is not recognized
    return cleaned;
  }
}

/**
 * Factory function to create NetGSM service with encrypted credentials
 */
export async function createNetGSMService(
  encryptedUsername: string,
  encryptedPassword: string,
  header: string,
  appname?: string
): Promise<NetGSMService> {
  try {
    const username = decryptText(encryptedUsername);
    const password = decryptText(encryptedPassword);
    
    return new NetGSMService({
      username,
      password,
      header,
      appname
    });
  } catch (error) {
    console.error('Failed to create NetGSM service:', error);
    throw new Error('NetGSM servis oluşturulamadı: Şifreli kimlik bilgileri çözülemedi');
  }
}

/**
 * Encrypt NetGSM credentials for database storage
 */
export function encryptNetGSMCredentials(username: string, password: string) {
  return {
    encryptedUsername: encryptText(username),
    encryptedPassword: encryptText(password)
  };
}
