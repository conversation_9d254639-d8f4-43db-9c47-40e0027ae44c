/**
 * SMS Appointment Cancellation API Route
 * Handles appointment cancellation via secure tokens sent in SMS links
 */

import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { z } from 'zod';
import { smsNotificationsService } from '@/lib/utils/sms-notifications';

// Route parameters interface
interface RouteParams {
  params: {
    token: string;
  };
}

// Cancellation confirmation schema
const confirmCancellationSchema = z.object({
  confirm: z.boolean(),
  reason: z.string().optional(),
  customerName: z.string().optional(),
  customerPhone: z.string().optional()
});

type ConfirmCancellationRequest = z.infer<typeof confirmCancellationSchema>;

/**
 * GET /api/sms/cancel/[token]
 * Validate cancellation token and get appointment details
 */
export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const cookieStore = await cookies();
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });

    const { token } = params;

    // Validate token format
    if (!token || token.length < 32) {
      return NextResponse.json({
        success: false,
        error: 'invalid_token',
        message: 'Geçersiz iptal bağlantısı'
      }, { status: 400 });
    }

    // Get client IP for security logging
    const clientIP = request.headers.get('x-forwarded-for') || 
                    request.headers.get('x-real-ip') || 
                    'unknown';
    const userAgent = request.headers.get('user-agent') || 'unknown';

    // Validate token using RPC function (this doesn't cancel yet, just validates)
    const { data: tokenData, error: tokenError } = await supabase
      .from('appointment_cancellation_tokens')
      .select(`
        id,
        appointment_id,
        salon_id,
        is_used,
        expires_at,
        appointments (
          id,
          date,
          start_time,
          end_time,
          status,
          fullname,
          phonenumber,
          email,
          services (
            name,
            duration,
            price
          ),
          barbers (
            name
          ),
          salons (
            name,
            phone,
            address
          )
        )
      `)
      .eq('token', token)
      .single();

    if (tokenError || !tokenData) {
      return NextResponse.json({
        success: false,
        error: 'token_not_found',
        message: 'İptal bağlantısı bulunamadı'
      }, { status: 404 });
    }

    // Check if token is already used
    if (tokenData.is_used) {
      return NextResponse.json({
        success: false,
        error: 'token_already_used',
        message: 'Bu iptal bağlantısı daha önce kullanılmış'
      }, { status: 410 });
    }

    // Check if token has expired
    const expiresAt = new Date(tokenData.expires_at);
    if (expiresAt < new Date()) {
      return NextResponse.json({
        success: false,
        error: 'token_expired',
        message: 'İptal bağlantısının süresi dolmuş'
      }, { status: 410 });
    }

    // Check if appointment exists and is not already cancelled
    const appointment = tokenData.appointments;
    if (!appointment) {
      return NextResponse.json({
        success: false,
        error: 'appointment_not_found',
        message: 'Randevu bulunamadı'
      }, { status: 404 });
    }

    if (appointment.status === 'cancelled') {
      return NextResponse.json({
        success: false,
        error: 'appointment_already_cancelled',
        message: 'Bu randevu zaten iptal edilmiş'
      }, { status: 410 });
    }

    // Check if appointment is in the past
    const appointmentDateTime = new Date(`${appointment.date}T${appointment.start_time}`);
    if (appointmentDateTime < new Date()) {
      return NextResponse.json({
        success: false,
        error: 'appointment_past',
        message: 'Geçmiş randevular iptal edilemez'
      }, { status: 400 });
    }

    // Return appointment details for confirmation
    return NextResponse.json({
      success: true,
      data: {
        appointment: {
          id: appointment.id,
          date: appointment.date,
          time: appointment.start_time,
          endTime: appointment.end_time,
          customerName: appointment.fullname,
          customerPhone: appointment.phonenumber,
          customerEmail: appointment.email,
          serviceName: appointment.services?.name,
          serviceDuration: appointment.services?.duration,
          servicePrice: appointment.services?.price,
          barberName: appointment.barbers?.name,
          salonName: appointment.salons?.name,
          salonPhone: appointment.salons?.phone,
          salonAddress: appointment.salons?.address
        },
        token: {
          id: tokenData.id,
          expiresAt: tokenData.expires_at
        }
      },
      message: 'Randevu bilgileri alındı'
    });

  } catch (error) {
    console.error('Token validation error:', error);
    return NextResponse.json({
      success: false,
      error: 'internal_error',
      message: 'Sunucu hatası oluştu'
    }, { status: 500 });
  }
}

/**
 * POST /api/sms/cancel/[token]
 * Confirm appointment cancellation
 */
export async function POST(request: NextRequest, { params }: RouteParams) {
  try {
    const cookieStore = await cookies();
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });

    const { token } = params;

    // Get client IP for security logging
    const clientIP = request.headers.get('x-forwarded-for') || 
                    request.headers.get('x-real-ip') || 
                    'unknown';
    const userAgent = request.headers.get('user-agent') || 'unknown';

    // Parse and validate request body
    let body: ConfirmCancellationRequest;
    try {
      const rawBody = await request.json();
      body = confirmCancellationSchema.parse(rawBody);
    } catch (error) {
      return NextResponse.json({
        success: false,
        error: 'validation_error',
        message: 'Geçersiz istek verisi',
        details: error instanceof z.ZodError ? error.errors : undefined
      }, { status: 400 });
    }

    // If not confirming, return early
    if (!body.confirm) {
      return NextResponse.json({
        success: false,
        error: 'cancellation_not_confirmed',
        message: 'İptal işlemi onaylanmadı'
      }, { status: 400 });
    }

    // Use RPC function to safely cancel the appointment
    const { data: cancelResult, error: cancelError } = await supabase.rpc('use_cancellation_token_public', {
      p_token: token,
      p_ip_address: clientIP,
      p_user_agent: userAgent
    });

    if (cancelError) {
      console.error('Cancellation RPC error:', cancelError);
      return NextResponse.json({
        success: false,
        error: 'cancellation_failed',
        message: 'İptal işlemi sırasında hata oluştu'
      }, { status: 500 });
    }

    if (!cancelResult?.success) {
      return NextResponse.json({
        success: false,
        error: cancelResult?.error || 'cancellation_failed',
        message: cancelResult?.message || 'İptal işlemi başarısız'
      }, { status: 400 });
    }

    // Get salon and appointment details for notification
    const { data: appointmentDetails, error: detailsError } = await supabase
      .from('appointments')
      .select(`
        id,
        date,
        start_time,
        fullname,
        phonenumber,
        services (name),
        barbers (name),
        salons (name, phone)
      `)
      .eq('id', cancelResult.appointment_id)
      .single();

    // Send cancellation confirmation SMS (non-blocking)
    if (appointmentDetails && !detailsError) {
      try {
        await smsNotificationsService.sendSMSNotification({
          type: 'appointment_cancelled',
          salonId: cancelResult.salon_id,
          recipientPhone: cancelResult.customer_phone,
          appointmentId: cancelResult.appointment_id,
          templateData: {
            salon_name: appointmentDetails.salons?.name || 'Salon',
            customer_name: cancelResult.customer_name,
            date: new Date(cancelResult.appointment_date).toLocaleDateString('tr-TR'),
            time: cancelResult.appointment_time,
            service_name: appointmentDetails.services?.name || 'Hizmet',
            barber_name: appointmentDetails.barbers?.name
          }
        });
      } catch (smsError) {
        console.error('Failed to send cancellation SMS:', smsError);
        // Don't fail the cancellation if SMS fails
      }
    }

    // Log the cancellation for audit purposes
    try {
      await supabase
        .from('sms_delivery_log')
        .insert({
          salon_id: cancelResult.salon_id,
          recipient_phone: cancelResult.customer_phone,
          message_content: `Randevu iptal edildi: ${cancelResult.appointment_date} ${cancelResult.appointment_time}`,
          message_type: 'appointment_cancelled',
          delivery_status: 'sent',
          appointment_id: cancelResult.appointment_id,
          ip_address: clientIP
        });
    } catch (logError) {
      console.error('Failed to log cancellation:', logError);
      // Don't fail the cancellation if logging fails
    }

    return NextResponse.json({
      success: true,
      data: {
        appointmentId: cancelResult.appointment_id,
        salonId: cancelResult.salon_id,
        customerName: cancelResult.customer_name,
        appointmentDate: cancelResult.appointment_date,
        appointmentTime: cancelResult.appointment_time
      },
      message: 'Randevunuz başarıyla iptal edildi'
    });

  } catch (error) {
    console.error('Appointment cancellation error:', error);
    return NextResponse.json({
      success: false,
      error: 'internal_error',
      message: 'İptal işlemi sırasında sunucu hatası oluştu'
    }, { status: 500 });
  }
}

/**
 * DELETE /api/sms/cancel/[token]
 * Alternative endpoint for cancellation (for compatibility)
 */
export async function DELETE(request: NextRequest, { params }: RouteParams) {
  // Redirect to POST method with confirm: true
  const body = { confirm: true };
  const newRequest = new NextRequest(request.url, {
    method: 'POST',
    headers: request.headers,
    body: JSON.stringify(body)
  });
  
  return POST(newRequest, { params });
}

/**
 * OPTIONS /api/sms/cancel/[token]
 * Handle CORS preflight requests
 */
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
