-- SMS Notification System Database Tables
-- Created: 2025-07-29 21:05:00
-- Description: Database schema for SMS notifications, OTP verification, and cancellation tokens

-- =====================================================
-- 1. OTP VERIFICATIONS TABLE
-- =====================================================
-- Stores OTP codes for customer booking verification
CREATE TABLE otp_verifications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    salon_id UUID NOT NULL REFERENCES salons(id) ON DELETE CASCADE,
    phone_number TEXT NOT NULL,
    otp_code TEXT NOT NULL,
    purpose TEXT NOT NULL DEFAULT 'booking_verification', -- booking_verification, password_reset, etc.
    is_verified BOOLEAN NOT NULL DEFAULT FALSE,
    attempts INTEGER NOT NULL DEFAULT 0,
    max_attempts INTEGER NOT NULL DEFAULT 3,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    verified_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Additional context data for the OTP (JSON format)
    context_data JSONB,
    
    -- IP address for security tracking
    ip_address INET,
    
    -- User agent for security tracking
    user_agent TEXT,
    
    CONSTRAINT otp_verifications_phone_format CHECK (phone_number ~ '^5[0-9]{9}$'),
    CONSTRAINT otp_verifications_otp_format CHECK (otp_code ~ '^[0-9]{6}$'),
    CONSTRAINT otp_verifications_attempts_check CHECK (attempts >= 0 AND attempts <= max_attempts),
    CONSTRAINT otp_verifications_expires_future CHECK (expires_at > created_at)
);

-- =====================================================
-- 2. APPOINTMENT CANCELLATION TOKENS TABLE
-- =====================================================
-- Stores secure tokens for appointment cancellation via SMS links
CREATE TABLE appointment_cancellation_tokens (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    appointment_id UUID NOT NULL REFERENCES appointments(id) ON DELETE CASCADE,
    salon_id UUID NOT NULL REFERENCES salons(id) ON DELETE CASCADE,
    token TEXT NOT NULL UNIQUE,
    is_used BOOLEAN NOT NULL DEFAULT FALSE,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    used_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- IP address when token was used
    used_ip_address INET,
    
    -- User agent when token was used
    used_user_agent TEXT,
    
    CONSTRAINT cancellation_tokens_expires_future CHECK (expires_at > created_at),
    CONSTRAINT cancellation_tokens_token_format CHECK (length(token) >= 32)
);

-- =====================================================
-- 3. SALON SMS SETTINGS TABLE
-- =====================================================
-- Stores NetGSM credentials and SMS configuration for each salon
CREATE TABLE salon_sms_settings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    salon_id UUID NOT NULL UNIQUE REFERENCES salons(id) ON DELETE CASCADE,
    
    -- NetGSM API Credentials (encrypted)
    netgsm_username_encrypted TEXT,
    netgsm_password_encrypted TEXT,
    netgsm_header TEXT, -- SMS sender header
    
    -- SMS Configuration
    is_enabled BOOLEAN NOT NULL DEFAULT FALSE,
    use_system_default BOOLEAN NOT NULL DEFAULT TRUE, -- Use system default NetGSM account
    
    -- SMS Templates (Turkish)
    otp_template TEXT DEFAULT 'SalonFlow doğrulama kodunuz: {otp_code}. Bu kod 5 dakika geçerlidir.',
    appointment_confirmation_template TEXT DEFAULT 'Randevunuz onaylandı! {salon_name} - {date} {time} - {service_name}. İptal: {cancel_link}',
    appointment_cancelled_template TEXT DEFAULT 'Randevunuz iptal edildi. {salon_name} - {date} {time} - {service_name}',
    appointment_updated_template TEXT DEFAULT 'Randevunuz güncellendi. {salon_name} - Yeni tarih: {date} {time} - {service_name}',
    
    -- Rate Limiting Settings
    daily_sms_limit INTEGER DEFAULT 1000,
    hourly_sms_limit INTEGER DEFAULT 100,
    
    -- Balance and Usage Tracking
    last_balance_check TIMESTAMP WITH TIME ZONE,
    current_balance DECIMAL(10,2),
    daily_sms_sent INTEGER DEFAULT 0,
    hourly_sms_sent INTEGER DEFAULT 0,
    last_daily_reset TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_hourly_reset TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Metadata
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES auth.users(id),
    updated_by UUID REFERENCES auth.users(id),
    
    CONSTRAINT sms_settings_limits_positive CHECK (
        daily_sms_limit > 0 AND 
        hourly_sms_limit > 0 AND 
        daily_sms_sent >= 0 AND 
        hourly_sms_sent >= 0
    ),
    CONSTRAINT sms_settings_balance_positive CHECK (current_balance >= 0)
);

-- =====================================================
-- 4. SMS DELIVERY LOG TABLE
-- =====================================================
-- Logs all SMS sending attempts for monitoring and debugging
CREATE TABLE sms_delivery_log (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    salon_id UUID NOT NULL REFERENCES salons(id) ON DELETE CASCADE,
    
    -- SMS Details
    recipient_phone TEXT NOT NULL,
    message_content TEXT NOT NULL,
    message_type TEXT NOT NULL, -- otp, confirmation, cancellation, update
    
    -- NetGSM Response
    netgsm_job_id TEXT, -- Job ID from NetGSM API
    delivery_status TEXT DEFAULT 'pending', -- pending, sent, delivered, failed
    netgsm_response JSONB, -- Full NetGSM API response
    
    -- Error Information
    error_code TEXT,
    error_message TEXT,
    retry_count INTEGER DEFAULT 0,
    max_retries INTEGER DEFAULT 3,
    
    -- Timing
    sent_at TIMESTAMP WITH TIME ZONE,
    delivered_at TIMESTAMP WITH TIME ZONE,
    failed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Context
    appointment_id UUID REFERENCES appointments(id) ON DELETE SET NULL,
    otp_verification_id UUID REFERENCES otp_verifications(id) ON DELETE SET NULL,
    
    -- Security
    ip_address INET,
    user_agent TEXT,
    
    CONSTRAINT sms_log_phone_format CHECK (recipient_phone ~ '^5[0-9]{9}$'),
    CONSTRAINT sms_log_retry_check CHECK (retry_count >= 0 AND retry_count <= max_retries),
    CONSTRAINT sms_log_status_check CHECK (delivery_status IN ('pending', 'sent', 'delivered', 'failed', 'cancelled'))
);

-- =====================================================
-- 5. SYSTEM SMS CONFIGURATION TABLE
-- =====================================================
-- Stores system-wide SMS configuration and default NetGSM credentials
CREATE TABLE system_sms_config (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- System Default NetGSM Credentials (encrypted)
    default_netgsm_username_encrypted TEXT,
    default_netgsm_password_encrypted TEXT,
    default_netgsm_header TEXT DEFAULT 'SALONFLOW',
    
    -- Global SMS Settings
    is_sms_enabled BOOLEAN NOT NULL DEFAULT TRUE,
    max_otp_attempts INTEGER DEFAULT 3,
    otp_expiry_minutes INTEGER DEFAULT 5,
    cancellation_token_expiry_hours INTEGER DEFAULT 24,
    
    -- Rate Limiting (Global)
    global_daily_limit INTEGER DEFAULT 10000,
    global_hourly_limit INTEGER DEFAULT 1000,
    
    -- Emergency Settings
    emergency_disable BOOLEAN DEFAULT FALSE,
    emergency_message TEXT,
    
    -- Metadata
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_by UUID REFERENCES auth.users(id),
    
    CONSTRAINT system_sms_limits_positive CHECK (
        max_otp_attempts > 0 AND 
        otp_expiry_minutes > 0 AND 
        cancellation_token_expiry_hours > 0 AND
        global_daily_limit > 0 AND 
        global_hourly_limit > 0
    )
);

-- Insert default system configuration
INSERT INTO system_sms_config (
    default_netgsm_header,
    is_sms_enabled,
    max_otp_attempts,
    otp_expiry_minutes,
    cancellation_token_expiry_hours
) VALUES (
    'SALONFLOW',
    TRUE,
    3,
    5,
    24
) ON CONFLICT DO NOTHING;

-- =====================================================
-- 6. UPDATE TRIGGERS FOR TIMESTAMPS
-- =====================================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Add update triggers for all tables
CREATE TRIGGER update_otp_verifications_updated_at 
    BEFORE UPDATE ON otp_verifications 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_cancellation_tokens_updated_at 
    BEFORE UPDATE ON appointment_cancellation_tokens 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_salon_sms_settings_updated_at 
    BEFORE UPDATE ON salon_sms_settings 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_sms_delivery_log_updated_at 
    BEFORE UPDATE ON sms_delivery_log 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_system_sms_config_updated_at 
    BEFORE UPDATE ON system_sms_config 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- 7. COMMENTS FOR DOCUMENTATION
-- =====================================================

COMMENT ON TABLE otp_verifications IS 'Stores OTP codes for customer booking verification and other purposes';
COMMENT ON TABLE appointment_cancellation_tokens IS 'Stores secure tokens for appointment cancellation via SMS links';
COMMENT ON TABLE salon_sms_settings IS 'Stores NetGSM credentials and SMS configuration for each salon';
COMMENT ON TABLE sms_delivery_log IS 'Logs all SMS sending attempts for monitoring and debugging';
COMMENT ON TABLE system_sms_config IS 'Stores system-wide SMS configuration and default NetGSM credentials';

COMMENT ON COLUMN otp_verifications.context_data IS 'Additional context data for the OTP in JSON format (e.g., appointment details)';
COMMENT ON COLUMN salon_sms_settings.use_system_default IS 'If true, uses system default NetGSM account instead of salon-specific credentials';
COMMENT ON COLUMN sms_delivery_log.netgsm_response IS 'Full NetGSM API response stored as JSON for debugging';
