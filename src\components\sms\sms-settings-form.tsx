/**
 * SMS Settings Form Component
 * Manages NetGSM credentials and SMS configuration for salons
 */

"use client"

import { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { toast } from 'sonner'
import { 
  MessageSquare, 
  Settings, 
  TestTube, 
  Eye, 
  EyeOff,
  Loader2,
  CheckCircle,
  AlertCircle,
  Info
} from 'lucide-react'

import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { Switch } from '@/components/ui/switch'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'

// SMS Settings Schema
const smsSettingsSchema = z.object({
  is_enabled: z.boolean().default(false),
  use_system_default: z.boolean().default(true),
  netgsm_username: z.string().optional(),
  netgsm_password: z.string().optional(),
  netgsm_header: z.string().min(1, 'SMS başlığı gereklidir').max(11, 'SMS başlığı en fazla 11 karakter olabilir').optional(),
  daily_sms_limit: z.coerce.number().min(1, 'Günlük limit en az 1 olmalıdır').max(10000, 'Günlük limit en fazla 10000 olabilir').default(1000),
  hourly_sms_limit: z.coerce.number().min(1, 'Saatlik limit en az 1 olmalıdır').max(1000, 'Saatlik limit en fazla 1000 olabilir').default(100),
  otp_template: z.string().min(10, 'OTP şablonu en az 10 karakter olmalıdır').optional(),
  appointment_confirmation_template: z.string().min(10, 'Onay şablonu en az 10 karakter olmalıdır').optional(),
  appointment_cancelled_template: z.string().min(10, 'İptal şablonu en az 10 karakter olmalıdır').optional(),
  appointment_updated_template: z.string().min(10, 'Güncelleme şablonu en az 10 karakter olmalıdır').optional()
})

type SMSSettingsForm = z.infer<typeof smsSettingsSchema>

interface SMSSettings {
  id: string
  salon_id: string
  is_enabled: boolean
  use_system_default: boolean
  netgsm_header: string
  daily_sms_limit: number
  hourly_sms_limit: number
  daily_sms_sent: number
  hourly_sms_sent: number
  current_balance: number | null
  last_balance_check: string | null
  otp_template: string
  appointment_confirmation_template: string
  appointment_cancelled_template: string
  appointment_updated_template: string
}

interface SMSSettingsFormProps {
  salonId: string
  salonName: string
}

export function SMSSettingsForm({ salonId, salonName }: SMSSettingsFormProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [isTesting, setIsTesting] = useState(false)
  const [isCheckingBalance, setIsCheckingBalance] = useState(false)
  const [settings, setSettings] = useState<SMSSettings | null>(null)
  const [isLoadingSettings, setIsLoadingSettings] = useState(true)
  const [showPassword, setShowPassword] = useState(false)
  const [testPhoneNumber, setTestPhoneNumber] = useState('')

  const form = useForm<SMSSettingsForm>({
    resolver: zodResolver(smsSettingsSchema),
    defaultValues: {
      is_enabled: false,
      use_system_default: true,
      netgsm_username: "",
      netgsm_password: "",
      netgsm_header: "",
      daily_sms_limit: 1000,
      hourly_sms_limit: 100,
      otp_template: "SalonFlow doğrulama kodunuz: {otp_code}. Bu kod {expiry_minutes} dakika geçerlidir.",
      appointment_confirmation_template: "Randevunuz onaylandı! {salon_name} - {date} {time} - {service_name}. İptal: {cancel_link}",
      appointment_cancelled_template: "Randevunuz iptal edildi. {salon_name} - {date} {time} - {service_name}",
      appointment_updated_template: "Randevunuz güncellendi. {salon_name} - Yeni tarih: {date} {time} - {service_name}"
    },
  })

  // Load existing settings
  useEffect(() => {
    loadSettings()
  }, [salonId])

  const loadSettings = async () => {
    try {
      setIsLoadingSettings(true)
      const response = await fetch(`/api/sms/settings?salon_id=${salonId}`)
      
      if (response.ok) {
        const result = await response.json()
        if (result.success && result.settings) {
          setSettings(result.settings)
          
          // Update form with loaded settings
          form.reset({
            is_enabled: result.settings.is_enabled,
            use_system_default: result.settings.use_system_default,
            netgsm_header: result.settings.netgsm_header || '',
            daily_sms_limit: result.settings.daily_sms_limit,
            hourly_sms_limit: result.settings.hourly_sms_limit,
            otp_template: result.settings.otp_template,
            appointment_confirmation_template: result.settings.appointment_confirmation_template,
            appointment_cancelled_template: result.settings.appointment_cancelled_template,
            appointment_updated_template: result.settings.appointment_updated_template
          })
        }
      }
    } catch (error) {
      console.error('Error loading SMS settings:', error)
      toast.error('SMS ayarları yüklenirken hata oluştu')
    } finally {
      setIsLoadingSettings(false)
    }
  }

  const onSubmit = async (data: SMSSettingsForm) => {
    try {
      setIsLoading(true)

      const response = await fetch(`/api/sms/settings?salon_id=${salonId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'Ayarlar kaydedilemedi')
      }

      setSettings(result.settings)
      toast.success('SMS ayarları başarıyla kaydedildi')
    } catch (error) {
      console.error('Error saving SMS settings:', error)
      toast.error(error instanceof Error ? error.message : 'Ayarlar kaydedilirken hata oluştu')
    } finally {
      setIsLoading(false)
    }
  }

  const handleTestSMS = async () => {
    if (!testPhoneNumber) {
      toast.error('Test için telefon numarası girin')
      return
    }

    try {
      setIsTesting(true)

      const response = await fetch('/api/sms/send', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          type: 'appointment_confirmation',
          salonId,
          recipientPhone: testPhoneNumber,
          templateData: {
            salon_name: salonName,
            customer_name: 'Test Müşteri',
            date: new Date().toLocaleDateString('tr-TR'),
            time: '14:00',
            service_name: 'Test Hizmeti',
            barber_name: 'Test Berber',
            cancel_link: 'https://example.com/cancel/test'
          }
        }),
      })

      const result = await response.json()

      if (result.success) {
        toast.success('Test SMS başarıyla gönderildi')
      } else {
        toast.error(result.message || 'Test SMS gönderilemedi')
      }
    } catch (error) {
      console.error('Error sending test SMS:', error)
      toast.error('Test SMS gönderilirken hata oluştu')
    } finally {
      setIsTesting(false)
    }
  }

  const handleCheckBalance = async () => {
    try {
      setIsCheckingBalance(true)

      const response = await fetch(`/api/sms/balance?salon_id=${salonId}`)
      const result = await response.json()

      if (result.success) {
        setSettings(prev => prev ? { ...prev, current_balance: result.balance, last_balance_check: new Date().toISOString() } : null)
        toast.success(`Bakiye: ₺${result.balance}`)
      } else {
        toast.error(result.message || 'Bakiye sorgulanamadı')
      }
    } catch (error) {
      console.error('Error checking balance:', error)
      toast.error('Bakiye sorgulanırken hata oluştu')
    } finally {
      setIsCheckingBalance(false)
    }
  }

  if (isLoadingSettings) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-12">
          <Loader2 className="h-8 w-8 animate-spin" />
          <span className="ml-2">SMS ayarları yükleniyor...</span>
        </CardContent>
      </Card>
    )
  }

  const useSystemDefault = form.watch('use_system_default')
  const isEnabled = form.watch('is_enabled')

  return (
    <div className="space-y-6">
      {/* Status Card */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <MessageSquare className="h-5 w-5" />
              <CardTitle>SMS Bildirimleri</CardTitle>
            </div>
            <Badge variant={settings?.is_enabled ? "default" : "secondary"}>
              {settings?.is_enabled ? "Aktif" : "Pasif"}
            </Badge>
          </div>
          <CardDescription>
            Randevu bildirimleri ve OTP doğrulama için SMS ayarları
          </CardDescription>
        </CardHeader>
        
        {settings && (
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div>
                <p className="text-muted-foreground">Günlük Gönderilen</p>
                <p className="font-medium">{settings.daily_sms_sent} / {settings.daily_sms_limit}</p>
              </div>
              <div>
                <p className="text-muted-foreground">Saatlik Gönderilen</p>
                <p className="font-medium">{settings.hourly_sms_sent} / {settings.hourly_sms_limit}</p>
              </div>
              <div>
                <p className="text-muted-foreground">Bakiye</p>
                <p className="font-medium">
                  {settings.current_balance !== null ? `₺${settings.current_balance}` : 'Bilinmiyor'}
                </p>
              </div>
              <div>
                <p className="text-muted-foreground">Son Kontrol</p>
                <p className="font-medium">
                  {settings.last_balance_check 
                    ? new Date(settings.last_balance_check).toLocaleDateString('tr-TR')
                    : 'Hiç'
                  }
                </p>
              </div>
            </div>
          </CardContent>
        )}
      </Card>

      {/* Settings Form */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Settings className="h-5 w-5" />
            <span>SMS Ayarları</span>
          </CardTitle>
        </CardHeader>
        
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              {/* Enable SMS */}
              <FormField
                control={form.control}
                name="is_enabled"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                    <div className="space-y-0.5">
                      <FormLabel className="text-base">SMS Bildirimleri</FormLabel>
                      <FormDescription>
                        Randevu olayları ve OTP doğrulama için SMS bildirimleri gönder
                      </FormDescription>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />

              {isEnabled && (
                <>
                  {/* Use System Default */}
                  <FormField
                    control={form.control}
                    name="use_system_default"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                        <div className="space-y-0.5">
                          <FormLabel className="text-base">Sistem Varsayılanını Kullan</FormLabel>
                          <FormDescription>
                            Kendi NetGSM hesabınız yerine sistem varsayılan hesabını kullan
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  {!useSystemDefault && (
                    <>
                      <Separator />
                      
                      {/* NetGSM Credentials */}
                      <div className="space-y-4">
                        <div className="flex items-center space-x-2">
                          <h3 className="text-lg font-medium">NetGSM Hesap Bilgileri</h3>
                          <Badge variant="outline">Kendi Hesabınız</Badge>
                        </div>
                        
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <FormField
                            control={form.control}
                            name="netgsm_username"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Kullanıcı Adı</FormLabel>
                                <FormControl>
                                  <Input {...field} placeholder="NetGSM kullanıcı adınız" />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={form.control}
                            name="netgsm_password"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Şifre</FormLabel>
                                <FormControl>
                                  <div className="relative">
                                    <Input 
                                      {...field} 
                                      type={showPassword ? "text" : "password"}
                                      placeholder="NetGSM şifreniz" 
                                    />
                                    <Button
                                      type="button"
                                      variant="ghost"
                                      size="sm"
                                      className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                                      onClick={() => setShowPassword(!showPassword)}
                                    >
                                      {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                                    </Button>
                                  </div>
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>
                      </div>
                    </>
                  )}

                  <Separator />

                  {/* SMS Header and Limits */}
                  <div className="space-y-4">
                    <h3 className="text-lg font-medium">SMS Yapılandırması</h3>
                    
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <FormField
                        control={form.control}
                        name="netgsm_header"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>SMS Başlığı</FormLabel>
                            <FormControl>
                              <Input {...field} placeholder="SALONFLOW" maxLength={11} />
                            </FormControl>
                            <FormDescription>En fazla 11 karakter</FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="daily_sms_limit"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Günlük Limit</FormLabel>
                            <FormControl>
                              <Input {...field} type="number" min="1" max="10000" />
                            </FormControl>
                            <FormDescription>Günde en fazla SMS sayısı</FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="hourly_sms_limit"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Saatlik Limit</FormLabel>
                            <FormControl>
                              <Input {...field} type="number" min="1" max="1000" />
                            </FormControl>
                            <FormDescription>Saatte en fazla SMS sayısı</FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </div>
                </>
              )}

              {/* Submit Button */}
              <div className="flex justify-end">
                <Button type="submit" disabled={isLoading}>
                  {isLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Kaydediliyor...
                    </>
                  ) : (
                    'Ayarları Kaydet'
                  )}
                </Button>
              </div>
            </form>
          </Form>
        </CardContent>
      </Card>

      {/* Test SMS */}
      {settings?.is_enabled && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <TestTube className="h-5 w-5" />
              <span>Test SMS</span>
            </CardTitle>
            <CardDescription>
              SMS ayarlarınızı test etmek için bir telefon numarasına test mesajı gönderin
            </CardDescription>
          </CardHeader>
          
          <CardContent>
            <div className="flex space-x-2">
              <Input
                placeholder="5XXXXXXXXX"
                value={testPhoneNumber}
                onChange={(e) => setTestPhoneNumber(e.target.value)}
                className="flex-1"
              />
              <Button onClick={handleTestSMS} disabled={isTesting || !testPhoneNumber}>
                {isTesting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Gönderiliyor...
                  </>
                ) : (
                  'Test Gönder'
                )}
              </Button>
              <Button variant="outline" onClick={handleCheckBalance} disabled={isCheckingBalance}>
                {isCheckingBalance ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  'Bakiye Sorgula'
                )}
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
