"use client"

import { useState, useEffect, useRef } from "react"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import { toast } from "sonner"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { SidebarTrigger } from "@/components/ui/sidebar"
import { Separator } from "@/components/ui/separator"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { getSupabaseBrowser } from "@/lib/supabase"
import { BookingLink } from "@/components/booking-link"
import { SMSSettingsForm } from "@/components/sms/sms-settings-form"
import { useUser } from "@/contexts/UserContext"
import { referrals, subscriptions } from "@/lib/db"

const supabase = getSupabaseBrowser();

// Form şemasını tanımla
const salonFormSchema = z.object({
  name: z.string().min(2, {
    message: "Salon adı en az 2 karakter olmalıdır.",
  }),
  address: z.string().min(5, {
    message: "Adres en az 5 karakter olmalıdır.",
  }),
  phone: z.string().min(5, {
    message: "Telefon numarası en az 5 karakter olmalıdır.",
  }),
  email: z.string().email({
    message: "Lütfen geçerli bir e-posta adresi girin.",
  }),
  website: z.string().url({
    message: "Lütfen geçerli bir URL girin.",
  }).optional().or(z.literal('')),
  description: z.string().optional(),
})

const notificationFormSchema = z.object({
  emailEnabled: z.boolean().default(true),
  smsEnabled: z.boolean().default(false),
  whatsappEnabled: z.boolean().default(false),
  reminderHours: z.coerce.number().min(1, {
    message: "Hatırlatma saati en az 1 olmalıdır.",
  }).max(72, {
    message: "Hatırlatma saati en fazla 72 olmalıdır.",
  }),
})

// Bildirim formu tipini tanımla
type NotificationFormValues = z.infer<typeof notificationFormSchema>

export default function SettingsPage() {
  const [activeTab, setActiveTab] = useState<"salon" | "notifications" | "sms" | "account" | "booking">("salon")
  const [isLoading, setIsLoading] = useState(true)
  const salonDataRef = useRef<z.infer<typeof salonFormSchema> | null>(null)

  // UserContext'ten salon bilgilerini al
  const { user, salon, salonId, salonLoading, refreshUser } = useUser()

  // Salon formunu başlat
  const salonForm = useForm<z.infer<typeof salonFormSchema>>({
    resolver: zodResolver(salonFormSchema),
    defaultValues: salonDataRef.current || {
      name: "Salonum",
      address: "Örnek Mahallesi, Ana Cadde No:123, İstanbul",
      phone: "+90 (555) 123 45 67",
      email: "<EMAIL>",
      website: "https://salonum.com",
      description: "Kaliteli saç kesimi ve bakım hizmetleri sunan profesyonel bir berber salonu.",
    },
  })

  // Salon verileri mevcut olduğunda form değerlerini ayarla
  useEffect(() => {
    if (salon && !salonLoading) {
      setIsLoading(true)

      // Form başlatma için salon verilerini sakla
      salonDataRef.current = {
        name: salon.name,
        address: salon.address || '',
        phone: salon.phone || '',
        email: salon.email || '',
        website: salon.website || '',
        description: salon.description || '',
      }

      // Formu salon verileriyle başlat
      salonForm.reset(salonDataRef.current)

      setIsLoading(false)
    }
  }, [salon, salonLoading, salonForm])

  // Salon verileri yüklendiğinde form değerlerini güncelle
  useEffect(() => {
    if (salonDataRef.current) {
      salonForm.reset(salonDataRef.current)
    }
  }, [salonForm, isLoading])

  // Bildirim formunu başlat
  const notificationForm = useForm<NotificationFormValues>({
    resolver: zodResolver(notificationFormSchema),
    defaultValues: {
      emailEnabled: true,
      smsEnabled: false,
      whatsappEnabled: false,
      reminderHours: 24,
    },
  })

  // Salon form gönderimini işle
  async function onSalonSubmit(values: z.infer<typeof salonFormSchema>) {
    try {
      if (!user) {
        toast.error("Kullanıcı bilgisi alınamadı.")
        return
      }

      if (salonId) {
        // Mevcut salonu güncelle
        await supabase
          .from('salons')
          .update({
            name: values.name,
            address: values.address,
            phone: values.phone,
            email: values.email,
            website: values.website || null,
            description: values.description || null,
            updated_at: new Date().toISOString()
          })
          .eq('id', salonId)

        toast.success("Salon bilgileri başarıyla güncellendi!")
      } else {
        // Yeni salon oluştur
        const { data, error } = await supabase
          .from('salons')
          .insert({
            name: values.name,
            address: values.address,
            phone: values.phone,
            email: values.email,
            website: values.website || null,
            description: values.description || null,
            owner_id: user.id
          })
          .select()
          .single()

        if (error) throw error

        // Salon için varsayılan çalışma saatlerini ayarla
        await setupDefaultWorkingHours(data.id)

        // Veritabanından bekleyen referans kodunu kontrol et ve uygula
        let isReferred = false
        try {
          // Kullanıcının bekleyen referans kodunu al
          const pendingReferral = await referrals.getPendingReferral(user.id)

          if (pendingReferral) {
            try {
              // Referans kodunu uygula
              await referrals.applyReferralCode(pendingReferral.referral_code, data.id)

              // Bekleyen referans kodunu uygulandı olarak işaretle
              await referrals.markPendingReferralAsApplied(pendingReferral.id)

              isReferred = true
              console.log(`Referral code ${pendingReferral.referral_code} applied for salon ${data.id}`)
              toast.success("Referans kodu başarıyla uygulandı!")
            } catch (error) {
              console.error("Referans kodu uygulanırken hata:", error)
            }
          } else {
            // Yedek mekanizma olarak sessionStorage'dan kontrol et
            if (typeof window !== 'undefined') {
              const referralCode = sessionStorage.getItem('referralCode')
              if (referralCode) {
                try {
                  // Referans kodunu uygula
                  await referrals.applyReferralCode(referralCode, data.id)

                  // Referans kodunu sessionStorage'dan temizle
                  sessionStorage.removeItem('referralCode')

                  isReferred = true
                  console.log(`Referral code ${referralCode} from sessionStorage applied for salon ${data.id}`)
                  toast.success("Referans kodu başarıyla uygulandı!")
                } catch (error) {
                  console.error("Referans kodu uygulanırken hata:", error)
                }
              }
            }
          }
        } catch (error) {
          console.error("Bekleyen referans kodu kontrol edilirken hata:", error)
        }

        // Deneme aboneliği oluştur
        try {
          // Solo plan ID'sini al
          const { data: soloPlans } = await supabase
            .from('subscription_plans')
            .select('id')
            .eq('name', 'Solo')
            .single()

          if (soloPlans) {
            await subscriptions.createTrialSubscription(data.id, soloPlans.id, isReferred)
            console.log("Deneme aboneliği başarıyla oluşturuldu")
          } else {
            console.error("Solo plan bulunamadı")
          }
        } catch (error) {
          console.error("Deneme aboneliği oluşturulurken hata:", error)
        }

        // Salon bilgilerini güncellemek için kullanıcı verilerini yenile
        await refreshUser()

        toast.success("Salon başarıyla oluşturuldu!")
      }
    } catch (error) {
      toast.error("Salon bilgileri kaydedilirken bir hata oluştu.")
      console.error(error)
    }
  }

  // Salon için varsayılan çalışma saatlerini ayarla
  async function setupDefaultWorkingHours(salonId: string) {
    try {
      const defaultHours = [
        { salon_id: salonId, day_of_week: 0, open_time: '09:00', close_time: '18:00', is_closed: true }, // Pazar
        { salon_id: salonId, day_of_week: 1, open_time: '09:00', close_time: '18:00', is_closed: false }, // Pazartesi
        { salon_id: salonId, day_of_week: 2, open_time: '09:00', close_time: '18:00', is_closed: false }, // Salı
        { salon_id: salonId, day_of_week: 3, open_time: '09:00', close_time: '18:00', is_closed: false }, // Çarşamba
        { salon_id: salonId, day_of_week: 4, open_time: '09:00', close_time: '18:00', is_closed: false }, // Perşembe
        { salon_id: salonId, day_of_week: 5, open_time: '09:00', close_time: '18:00', is_closed: false }, // Cuma
        { salon_id: salonId, day_of_week: 6, open_time: '09:00', close_time: '18:00', is_closed: false }, // Cumartesi
      ]

      await supabase
        .from('working_hours')
        .insert(defaultHours)
    } catch (error) {
      console.error("Varsayılan çalışma saatleri ayarlanırken hata:", error)
    }
  }

  // Bildirim form gönderimini işle
  async function onNotificationSubmit(values: NotificationFormValues) {
    try {
      // Gerçek bir uygulamada, burada Supabase'e kaydederdik
      console.log(values)
      toast.success("Bildirim ayarları başarıyla güncellendi!")
    } catch (error) {
      toast.error("Bildirim ayarları güncellenirken bir hata oluştu")
      console.error(error)
    }
  }

  return (
    <div className="p-4 space-y-6">
      <header className="flex h-16 shrink-0 items-center gap-2 mb-4">
        <div className="flex items-center gap-2">
          <SidebarTrigger className="-ml-1" />
          <Separator
            orientation="vertical"
            className="mr-2 data-[orientation=vertical]:h-4"
          />
          <h1 className="text-2xl font-bold">{!salonId ? "Salon Oluştur" : "Ayarlar"}</h1>
        </div>
      </header>

      {!salonId && (
        <div className="bg-muted/30 p-4 rounded-lg mb-6">
          <div className="flex items-start space-x-4">
            <div className="bg-primary/10 p-2 rounded-full">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-primary"><circle cx="12" cy="12" r="10"></circle><path d="M12 16v-4"></path><path d="M12 8h.01"></path></svg>
            </div>
            <div>
              <h3 className="font-medium">Başlamak için salonunuzu oluşturun</h3>
              <p className="text-sm text-muted-foreground mt-1">
                Salonunuzu oluşturduktan sonra randevu almaya, müşteri eklemeye ve diğer özellikleri kullanmaya başlayabilirsiniz.
              </p>
            </div>
          </div>
        </div>
      )}

      <div className="flex border-b space-x-8">
        <button
          className={`pb-2 font-medium ${
            activeTab === "salon"
              ? "border-b-2 border-primary text-foreground"
              : "text-muted-foreground"
          }`}
          onClick={() => setActiveTab("salon")}
        >
          Salon Bilgileri
        </button>
        <button
          className={`pb-2 font-medium ${
            activeTab === "notifications"
              ? "border-b-2 border-primary text-foreground"
              : "text-muted-foreground"
          }`}
          onClick={() => setActiveTab("notifications")}
        >
          Bildirimler
        </button>
        <button
          className={`pb-2 font-medium ${
            activeTab === "sms"
              ? "border-b-2 border-primary text-foreground"
              : "text-muted-foreground"
          }`}
          onClick={() => setActiveTab("sms")}
        >
          SMS Ayarları
        </button>
        <button
          className={`pb-2 font-medium ${
            activeTab === "booking"
              ? "border-b-2 border-primary text-foreground"
              : "text-muted-foreground"
          }`}
          onClick={() => setActiveTab("booking")}
        >
          Randevu
        </button>
        <button
          className={`pb-2 font-medium ${
            activeTab === "account"
              ? "border-b-2 border-primary text-foreground"
              : "text-muted-foreground"
          }`}
          onClick={() => setActiveTab("account")}
        >
          Hesap
        </button>
      </div>

      {activeTab === "salon" && (
        <Card>
          <CardHeader>
            <CardTitle>Salon Bilgileri</CardTitle>
            <CardDescription>
              Salonunuzun temel bilgilerini güncelleyin. Bu bilgiler müşterilerinize gösterilecektir.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Form {...salonForm}>
              <form onSubmit={salonForm.handleSubmit(onSalonSubmit)} className="space-y-4">
                <FormField
                  control={salonForm.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Salon Adı</FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={salonForm.control}
                  name="address"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Adres</FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={salonForm.control}
                    name="phone"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Telefon</FormLabel>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={salonForm.control}
                    name="email"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>E-posta</FormLabel>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                <FormField
                  control={salonForm.control}
                  name="website"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Web Sitesi (İsteğe Bağlı)</FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={salonForm.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Açıklama (İsteğe Bağlı)</FormLabel>
                      <FormControl>
                        <Textarea
                          {...field}
                          rows={4}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <div className="flex justify-end">
                  <Button type="submit" size={!salonId ? "lg" : "default"} className={!salonId ? "px-8" : ""}>
                    {!salonId ? "Salon Oluştur" : "Değişiklikleri Kaydet"}
                  </Button>
                </div>
              </form>
            </Form>
          </CardContent>
        </Card>
      )}

      {activeTab === "notifications" && (
        <Card>
          <CardHeader>
            <CardTitle>Bildirim Ayarları</CardTitle>
            <CardDescription>
              Müşterilerinizin randevu hatırlatmalarını nasıl ve ne zaman alacağını yapılandırın.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Form {...notificationForm}>
              <form onSubmit={notificationForm.handleSubmit(onNotificationSubmit)} className="space-y-4">
                <FormField
                  control={notificationForm.control}
                  name="emailEnabled"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                      <div className="space-y-0.5">
                        <FormLabel className="text-base">E-posta Bildirimleri</FormLabel>
                        <FormDescription>
                          Randevu hatırlatmalarını e-posta ile gönderin.
                        </FormDescription>
                      </div>
                      <FormControl>
                        <input
                          type="checkbox"
                          checked={field.value}
                          onChange={field.onChange}
                          className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
                <FormField
                  control={notificationForm.control}
                  name="smsEnabled"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                      <div className="space-y-0.5">
                        <FormLabel className="text-base">SMS Bildirimleri</FormLabel>
                        <FormDescription>
                          Randevu hatırlatmalarını SMS ile gönderin.
                        </FormDescription>
                      </div>
                      <FormControl>
                        <input
                          type="checkbox"
                          checked={field.value}
                          onChange={field.onChange}
                          className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
                <FormField
                  control={notificationForm.control}
                  name="whatsappEnabled"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                      <div className="space-y-0.5">
                        <FormLabel className="text-base">WhatsApp Bildirimleri</FormLabel>
                        <FormDescription>
                          Randevu hatırlatmalarını WhatsApp ile gönderin.
                        </FormDescription>
                      </div>
                      <FormControl>
                        <input
                          type="checkbox"
                          checked={field.value}
                          onChange={field.onChange}
                          className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
                <FormField
                  control={notificationForm.control}
                  name="reminderHours"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Hatırlatma Zamanı (Randevudan Kaç Saat Önce)</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          min={1}
                          max={72}
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        Hatırlatmaların randevudan kaç saat önce gönderilmesi gerektiği.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <div className="flex justify-end">
                  <Button type="submit">Değişiklikleri Kaydet</Button>
                </div>
              </form>
            </Form>
          </CardContent>
        </Card>
      )}

      {activeTab === "sms" && (
        <>
          {isLoading ? (
            <div className="flex justify-center items-center h-64">
              <p>Yükleniyor...</p>
            </div>
          ) : salonId ? (
            <SMSSettingsForm salonId={salonId} salonName={salon?.name || 'Salon'} />
          ) : (
            <Card>
              <CardHeader>
                <CardTitle>SMS Ayarları</CardTitle>
                <CardDescription>
                  SMS ayarlarını yapılandırmak için önce bir salon oluşturmanız gerekiyor.
                </CardDescription>
              </CardHeader>
            </Card>
          )}
        </>
      )}

      {activeTab === "booking" && (
        <>
          {isLoading ? (
            <div className="flex justify-center items-center h-64">
              <p>Yükleniyor...</p>
            </div>
          ) : salonId ? (
            <BookingLink salonId={salonId} />
          ) : (
            <Card>
              <CardHeader>
                <CardTitle>Randevu Linki</CardTitle>
                <CardDescription>
                  Randevu linki almak için önce bir salon oluşturmanız gerekiyor.
                </CardDescription>
              </CardHeader>
            </Card>
          )}
        </>
      )}

      {activeTab === "account" && (
        <Card>
          <CardHeader>
            <CardTitle>Hesap Ayarları</CardTitle>
            <CardDescription>
              Hesap ayarlarınızı ve aboneliğinizi yönetin.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              <div className="space-y-2">
                <h3 className="text-lg font-medium">Mevcut Plan</h3>
                <div className="rounded-lg border p-4">
                  <div className="flex justify-between items-center">
                    <div>
                      <p className="font-medium">Standart Plan</p>
                      <p className="text-sm text-muted-foreground">39₺/ay</p>
                    </div>
                    <Button variant="outline">Yükselt</Button>
                  </div>
                  <div className="mt-4 text-sm text-muted-foreground">
                    <p>Planınız şunları içerir:</p>
                    <ul className="list-disc list-inside mt-2 space-y-1">
                      <li>5 Personele Kadar</li>
                      <li>Sınırsız Randevu</li>
                      <li>E-posta ve SMS Hatırlatmaları</li>
                      <li>Gelişmiş Analitik</li>
                      <li>Müşteri Yönetimi</li>
                    </ul>
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <h3 className="text-lg font-medium">Şifre</h3>
                <Button variant="outline">Şifre Değiştir</Button>
              </div>

              <div className="space-y-2">
                <h3 className="text-lg font-medium">Tehlikeli Bölge</h3>
                <div className="rounded-lg border border-destructive/20 p-4">
                  <div className="flex justify-between items-center">
                    <div>
                      <p className="font-medium text-destructive">Hesabı Sil</p>
                      <p className="text-sm text-muted-foreground">
                        Hesabınızı ve ilişkili tüm verileri kalıcı olarak silin.
                      </p>
                    </div>
                    <Button variant="destructive">Hesabı Sil</Button>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
