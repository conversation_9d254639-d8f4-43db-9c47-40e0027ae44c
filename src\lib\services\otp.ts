/**
 * OTP (One-Time Password) Service
 * Handles OTP generation, validation, and management for SMS verification
 */

import crypto from 'crypto';

// OTP Configuration Interface
export interface OTPConfig {
  length: number;
  expiryMinutes: number;
  maxAttempts: number;
  purpose: string;
}

// OTP Generation Result
export interface OTPGenerationResult {
  success: boolean;
  otpCode?: string;
  expiresAt?: Date;
  otpId?: string;
  error?: string;
  message?: string;
}

// OTP Validation Result
export interface OTPValidationResult {
  success: boolean;
  isValid?: boolean;
  otpId?: string;
  contextData?: any;
  error?: string;
  message?: string;
  attemptsRemaining?: number;
}

// Default OTP Configuration
const DEFAULT_OTP_CONFIG: OTPConfig = {
  length: 6,
  expiryMinutes: 5,
  maxAttempts: 3,
  purpose: 'booking_verification'
};

/**
 * OTP Service Class
 * Provides methods for OTP generation, validation, and management
 */
export class OTPService {
  private config: OTPConfig;

  constructor(config: Partial<OTPConfig> = {}) {
    this.config = { ...DEFAULT_OTP_CONFIG, ...config };
  }

  /**
   * Generate a secure OTP code
   * @param customLength Optional custom length for the OTP
   * @returns Generated OTP code as string
   */
  generateOTPCode(customLength?: number): string {
    const length = customLength || this.config.length;
    
    // Use crypto.randomInt for secure random number generation
    let otp = '';
    for (let i = 0; i < length; i++) {
      otp += crypto.randomInt(0, 10).toString();
    }
    
    return otp;
  }

  /**
   * Calculate expiration time for OTP
   * @param customMinutes Optional custom expiry minutes
   * @returns Date object representing expiration time
   */
  calculateExpiryTime(customMinutes?: number): Date {
    const minutes = customMinutes || this.config.expiryMinutes;
    const expiryTime = new Date();
    expiryTime.setMinutes(expiryTime.getMinutes() + minutes);
    return expiryTime;
  }

  /**
   * Validate OTP code format
   * @param otpCode The OTP code to validate
   * @returns True if format is valid
   */
  isValidOTPFormat(otpCode: string): boolean {
    const expectedLength = this.config.length;
    const otpRegex = new RegExp(`^[0-9]{${expectedLength}}$`);
    return otpRegex.test(otpCode);
  }

  /**
   * Check if OTP has expired
   * @param expiresAt Expiration timestamp
   * @returns True if OTP has expired
   */
  isOTPExpired(expiresAt: Date): boolean {
    return new Date() > expiresAt;
  }

  /**
   * Check if maximum attempts have been reached
   * @param attempts Current attempt count
   * @param maxAttempts Maximum allowed attempts
   * @returns True if max attempts reached
   */
  isMaxAttemptsReached(attempts: number, maxAttempts?: number): boolean {
    const max = maxAttempts || this.config.maxAttempts;
    return attempts >= max;
  }

  /**
   * Generate OTP verification message template
   * @param otpCode The OTP code
   * @param expiryMinutes Expiry time in minutes
   * @param salonName Optional salon name
   * @returns Formatted SMS message
   */
  generateOTPMessage(
    otpCode: string, 
    expiryMinutes?: number, 
    salonName?: string
  ): string {
    const minutes = expiryMinutes || this.config.expiryMinutes;
    const salon = salonName ? ` (${salonName})` : '';
    
    return `SalonFlow${salon} doğrulama kodunuz: ${otpCode}. Bu kod ${minutes} dakika geçerlidir. Kodu kimseyle paylaşmayın.`;
  }

  /**
   * Generate a secure token for OTP identification
   * @returns Random UUID-like token
   */
  generateOTPToken(): string {
    return crypto.randomUUID();
  }

  /**
   * Rate limiting check for OTP requests
   * @param phoneNumber Phone number to check
   * @param timeWindowMinutes Time window for rate limiting
   * @param maxRequestsPerWindow Maximum requests allowed in time window
   * @returns Rate limit status
   */
  checkRateLimit(
    phoneNumber: string,
    timeWindowMinutes: number = 60,
    maxRequestsPerWindow: number = 5
  ): { allowed: boolean; resetTime?: Date; requestsRemaining?: number } {
    // This would typically check against a cache/database
    // For now, we'll return allowed=true and implement the actual logic in the API
    return {
      allowed: true,
      resetTime: new Date(Date.now() + timeWindowMinutes * 60 * 1000),
      requestsRemaining: maxRequestsPerWindow - 1
    };
  }

  /**
   * Sanitize phone number for OTP operations
   * @param phoneNumber Raw phone number input
   * @returns Sanitized phone number
   */
  sanitizePhoneNumber(phoneNumber: string): string {
    // Remove all non-digit characters
    const cleaned = phoneNumber.replace(/\D/g, '');
    
    // Handle Turkish phone number formats
    if (cleaned.startsWith('905')) {
      return cleaned.substring(2); // Remove country code +90
    } else if (cleaned.startsWith('90') && cleaned.length === 12) {
      return cleaned.substring(2); // Remove country code 90
    } else if (cleaned.startsWith('0') && cleaned.length === 11) {
      return cleaned.substring(1); // Remove leading 0
    } else if (cleaned.startsWith('5') && cleaned.length === 10) {
      return cleaned; // Already in correct format
    }
    
    return cleaned;
  }

  /**
   * Validate Turkish phone number format
   * @param phoneNumber Phone number to validate
   * @returns True if valid Turkish mobile number
   */
  isValidTurkishPhone(phoneNumber: string): boolean {
    const sanitized = this.sanitizePhoneNumber(phoneNumber);
    const turkishMobileRegex = /^5[0-9]{9}$/;
    return turkishMobileRegex.test(sanitized);
  }

  /**
   * Generate context data for OTP
   * @param purpose OTP purpose
   * @param additionalData Any additional context data
   * @returns Context data object
   */
  generateContextData(purpose: string, additionalData: any = {}): any {
    return {
      purpose,
      generatedAt: new Date().toISOString(),
      version: '1.0',
      ...additionalData
    };
  }

  /**
   * Create OTP verification payload for database storage
   * @param salonId Salon ID
   * @param phoneNumber Phone number
   * @param purpose OTP purpose
   * @param contextData Additional context data
   * @param ipAddress Client IP address
   * @param userAgent Client user agent
   * @returns OTP verification payload
   */
  createOTPPayload(
    salonId: string,
    phoneNumber: string,
    purpose: string = 'booking_verification',
    contextData: any = {},
    ipAddress?: string,
    userAgent?: string
  ) {
    const otpCode = this.generateOTPCode();
    const expiresAt = this.calculateExpiryTime();
    const sanitizedPhone = this.sanitizePhoneNumber(phoneNumber);
    
    return {
      salon_id: salonId,
      phone_number: sanitizedPhone,
      otp_code: otpCode,
      purpose,
      expires_at: expiresAt.toISOString(),
      context_data: this.generateContextData(purpose, contextData),
      ip_address: ipAddress,
      user_agent: userAgent,
      max_attempts: this.config.maxAttempts
    };
  }

  /**
   * Validate OTP verification payload
   * @param payload OTP verification payload
   * @returns Validation result
   */
  validateOTPPayload(payload: any): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Check required fields
    if (!payload.salon_id) errors.push('Salon ID gerekli');
    if (!payload.phone_number) errors.push('Telefon numarası gerekli');
    if (!payload.otp_code) errors.push('OTP kodu gerekli');

    // Validate phone number format
    if (payload.phone_number && !this.isValidTurkishPhone(payload.phone_number)) {
      errors.push('Geçersiz telefon numarası formatı');
    }

    // Validate OTP code format
    if (payload.otp_code && !this.isValidOTPFormat(payload.otp_code)) {
      errors.push('Geçersiz OTP kodu formatı');
    }

    // Validate expiration time
    if (payload.expires_at) {
      const expiresAt = new Date(payload.expires_at);
      if (isNaN(expiresAt.getTime()) || expiresAt <= new Date()) {
        errors.push('Geçersiz veya geçmiş son kullanma tarihi');
      }
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  /**
   * Generate OTP resend message
   * @param attemptsRemaining Remaining attempts
   * @param nextResendTime Next allowed resend time
   * @returns Formatted message
   */
  generateResendMessage(attemptsRemaining: number, nextResendTime?: Date): string {
    let message = `Doğrulama kodu yeniden gönderildi. Kalan deneme hakkınız: ${attemptsRemaining}`;
    
    if (nextResendTime) {
      const minutes = Math.ceil((nextResendTime.getTime() - Date.now()) / (1000 * 60));
      if (minutes > 0) {
        message += `. Yeni kod ${minutes} dakika sonra talep edebilirsiniz.`;
      }
    }
    
    return message;
  }

  /**
   * Generate OTP error messages in Turkish
   * @param errorType Type of error
   * @param context Additional context
   * @returns Localized error message
   */
  getErrorMessage(errorType: string, context?: any): string {
    const messages: { [key: string]: string } = {
      'invalid_format': 'Geçersiz doğrulama kodu formatı',
      'expired': 'Doğrulama kodunun süresi dolmuş',
      'max_attempts': 'Maksimum deneme sayısına ulaşıldı',
      'not_found': 'Doğrulama kodu bulunamadı',
      'already_verified': 'Bu kod zaten doğrulanmış',
      'rate_limited': 'Çok fazla istek. Lütfen daha sonra tekrar deneyin',
      'invalid_phone': 'Geçersiz telefon numarası',
      'system_error': 'Sistem hatası oluştu',
      'network_error': 'Ağ hatası oluştu'
    };

    return messages[errorType] || 'Bilinmeyen hata oluştu';
  }
}

// Export default instance
export const otpService = new OTPService();

// Export factory function for custom configurations
export function createOTPService(config: Partial<OTPConfig>): OTPService {
  return new OTPService(config);
}
