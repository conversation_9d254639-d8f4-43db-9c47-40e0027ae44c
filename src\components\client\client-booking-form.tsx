"use client"

import { useState, useEffect } from "react"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import { format, addMinutes, parse } from "date-fns"
import * as z from "zod"
import { toast } from "sonner"
import { motion } from "framer-motion"
import { notifyNewAppointment, formatAppointmentForNotification } from "@/lib/utils/telegram-notifications"
import { generateAppointmentCancellationToken } from "@/lib/utils/cancellation-tokens"

import { Button } from "@/components/ui/button"
import { Form } from "@/components/ui/form"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { ServiceSelector } from "@/components/client/service-selector"
import { BarberSelector } from "@/components/client/barber-selector"
import { DateSelector } from "@/components/client/date-selector"
import { TimeSelector } from "@/components/client/time-selector"
import { CustomerInfoForm } from "@/components/client/customer-info-form"
import { BookingSummary } from "@/components/client/booking-summary"
import { StepIndicator } from "@/components/client/step-indicator"
import { OTPInputWithTimer } from "@/components/ui/otp-input"
import {
  appointments,
  publicAccess,
  Barber,
  Service,
  BarberWorkingHours
} from "@/lib/db"
import { supabaseClient } from "@/lib/supabase-singleton"

// Form schema
const formSchema = z.object({
  date: z.date({
    required_error: "Tarih seçmelisiniz.",
  }).optional(),
  start_time: z.string({
    required_error: "Saat seçmelisiniz.",
  }).optional().or(z.literal("")),
  barber_id: z.string({
    required_error: "Berber seçmelisiniz.",
  }).optional().or(z.literal("")),
  service_id: z.string({
    required_error: "Hizmet seçmelisiniz.",
  }).optional().or(z.literal("")),
  name: z.string().min(2, {
    message: "İsim en az 2 karakter olmalıdır.",
  }),
  surname: z.string().min(2, {
    message: "Soyisim en az 2 karakter olmalıdır.",
  }),
  phone: z.string().min(10, {
    message: "Geçerli bir telefon numarası giriniz.",
  }),
  email: z.string().email({
    message: "Geçerli bir e-posta adresi giriniz.",
  }).optional().or(z.literal("")),
})

interface ClientBookingFormProps {
  salonId: string
  onSuccess?: () => void
}

type BookingStep = "service" | "barber" | "date" | "time" | "info" | "otp" | "summary"

export function ClientBookingForm({ salonId, onSuccess }: ClientBookingFormProps) {
  const [currentStep, setCurrentStep] = useState<BookingStep>("service")
  const [isLoading, setIsLoading] = useState(false)
  const [barbersList, setBarbersList] = useState<Barber[]>([])
  const [servicesList, setServicesList] = useState<Service[]>([])
  const [selectedService, setSelectedService] = useState<Service | null>(null)
  const [availableTimes, setAvailableTimes] = useState<string[]>([])
  const [selectedDate, setSelectedDate] = useState<Date | null>(null)
  const [selectedBarber, setSelectedBarber] = useState<string | null>(null)
  const [disabledDates, setDisabledDates] = useState<Date[]>([])
  const [holidayDates, setHolidayDates] = useState<{ date: Date; description: string }[]>([])
  const [barberWorkingHoursList, setBarberWorkingHoursList] = useState<BarberWorkingHours[]>([])
  const [existingAppointmentTimes, setExistingAppointmentTimes] = useState<string[]>([])

  // OTP verification state
  const [otpCode, setOtpCode] = useState("")
  const [otpId, setOtpId] = useState<string | null>(null)
  const [otpExpiryTime, setOtpExpiryTime] = useState<Date | null>(null)
  const [isOtpLoading, setIsOtpLoading] = useState(false)
  const [isOtpVerifying, setIsOtpVerifying] = useState(false)
  const [otpError, setOtpError] = useState<string | null>(null)
  const [isOtpVerified, setIsOtpVerified] = useState(false)

  // Initialize form
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: "",
      surname: "",
      phone: "",
      email: "",
      start_time: "",
      service_id: "",
      barber_id: ""
    },
  })

  // Load services and barbers
  useEffect(() => {
    async function loadData() {
      try {
        // Load services
        const servicesData = await publicAccess.getPublicServicesBySalonId(salonId)
        setServicesList(servicesData)

        // Load barbers
        const barbersData = await publicAccess.getPublicBarbersBySalonId(salonId)
        setBarbersList(barbersData)
      } catch (error) {
        console.error("Error loading data:", error)
        toast.error("Veriler yüklenirken bir hata oluştu.")
      }
    }

    loadData()
  }, [salonId])

  // Handle service selection
  const handleServiceChange = (serviceId: string) => {
    const service = servicesList.find(s => s.id === serviceId)
    setSelectedService(service || null)
    form.setValue("service_id", serviceId)

    // Reset barber selection if service changes
    if (form.getValues("barber_id") && currentStep === "service") {
      form.setValue("barber_id", "")
      setSelectedBarber(null)
    }

    // Move to next step
    if (currentStep === "service") {
      setCurrentStep("barber")
    }
  }

  // Handle barber selection
  const handleBarberChange = async (barberId: string) => {
    setSelectedBarber(barberId)
    form.setValue("barber_id", barberId)
    form.setValue("start_time", "")

    // Load barber working hours
    await loadBarberWorkingHours(barberId)

    // Load available times when both date and barber are selected
    if (selectedDate && barberId) {
      await loadAvailableTimes(selectedDate, barberId)
    }

    // Move to next step
    if (currentStep === "barber") {
      setCurrentStep("date")
    }
  }

  // Handle date selection
  const handleDateChange = async (date: Date) => {
    setSelectedDate(date)
    form.setValue("date", date)
    form.setValue("start_time", "")

    // Load available times when both date and barber are selected
    if (date && selectedBarber) {
      await loadAvailableTimes(date, selectedBarber)
    }

    // Move to next step only if we have a barber selected
    if (currentStep === "date" && selectedBarber) {
      setCurrentStep("time")
    }
  }

  // Handle time selection
  const handleTimeChange = (time: string) => {
    form.setValue("start_time", time)

    // Move to next step
    if (currentStep === "time") {
      setCurrentStep("info")
    }
  }

  // Handle customer info submission
  const handleCustomerInfoSubmit = async (data: { name: string, surname: string, phone: string, email: string }) => {
    form.setValue("name", data.name)
    form.setValue("surname", data.surname)
    form.setValue("phone", data.phone)
    form.setValue("email", data.email || "")

    // Move to OTP verification step
    setCurrentStep("otp")

    // Generate and send OTP
    await generateOTP(data.phone)
  }

  // Load barber working hours and set disabled dates
  const loadBarberWorkingHours = async (barberId: string) => {
    try {
      // Berber çalışma saatlerini getir
      // Not: Bu fonksiyon SQL fonksiyonları Supabase'de çalıştırıldıktan sonra çalışacaktır
      let barberWorkingHours = []
      try {
        const { data, error } = await supabaseClient
          .from('barber_working_hours')
          .select('*')
          .eq('barber_id', barberId)
          .order('day_of_week')

        if (!error) {
          barberWorkingHours = data || []
        }
      } catch (err) {
        console.error("Error fetching barber working hours:", err)
      }

      // Salon çalışma saatlerini getir
      const salonWorkingHours = await publicAccess.getPublicWorkingHoursBySalonId(salonId)

      // Berber çalışma saatleri varsa onları, yoksa salon çalışma saatlerini kullan
      const hoursToUse = barberWorkingHours.length > 0
        ? barberWorkingHours.map(bwh => ({
            id: bwh.id,
            barber_id: bwh.barber_id,
            salon_id: salonId,
            day_of_week: bwh.day_of_week,
            open_time: bwh.open_time,
            close_time: bwh.close_time,
            is_closed: bwh.is_closed,
            lunch_start_time: bwh.lunch_start_time,
            lunch_end_time: bwh.lunch_end_time,
            has_lunch_break: bwh.has_lunch_break
          }))
        : salonWorkingHours.map(wh => ({
            id: wh.id,
            barber_id: barberId,
            salon_id: wh.salon_id,
            day_of_week: wh.day_of_week,
            open_time: wh.open_time,
            close_time: wh.close_time,
            is_closed: wh.is_closed,
            lunch_start_time: null,
            lunch_end_time: null,
            has_lunch_break: false
          }))

      // Set barber working hours list for TimeSlotGrid
      setBarberWorkingHoursList(hoursToUse || [])

      // All days of the week (0-6)
      const allDaysOfWeek = [0, 1, 2, 3, 4, 5, 6]

      // Days that have records in the database
      const daysWithRecords = hoursToUse.map(day => day.day_of_week)

      // Days that don't have records in the database (considered closed)
      const daysWithoutRecords = allDaysOfWeek.filter(day => !daysWithRecords.includes(day))

      // Days that are explicitly marked as closed
      const explicitlyClosedDays = hoursToUse
        .filter(day => day.is_closed)
        .map(day => day.day_of_week)

      // All closed days (either no record or explicitly marked as closed)
      const closedDays = [...new Set([...daysWithoutRecords, ...explicitlyClosedDays])]

      // Generate disabled dates for the next 3 months
      const disabledDatesArray: Date[] = []
      const today = new Date()
      const threeMonthsLater = new Date()
      threeMonthsLater.setMonth(today.getMonth() + 3)

      // Get salon holidays
      const holidaysData = await publicAccess.getPublicHolidaysBySalonId(salonId)

      // Format holiday dates
      const holidayDateObjects = (holidaysData || []).map(holiday => new Date(holiday.date))
      const holidayDateFormatted = (holidaysData || []).map(holiday => ({
        date: new Date(holiday.date),
        description: holiday.description || 'Tatil'
      }))

      setHolidayDates(holidayDateFormatted)

      // Loop through each day in the next 3 months
      const currentDate = new Date(today)
      while (currentDate <= threeMonthsLater) {
        const dayOfWeek = currentDate.getDay()
        const currentDateStr = format(currentDate, "yyyy-MM-dd")

        // If the barber doesn't work on this day of the week, add it to disabled dates
        if (closedDays.includes(dayOfWeek)) {
          // Create a new Date object to avoid reference issues
          disabledDatesArray.push(new Date(currentDate.getTime()))
        }
        // Check if the date is a holiday
        else if (holidayDateObjects.some(date => format(date, "yyyy-MM-dd") === currentDateStr)) {
          disabledDatesArray.push(new Date(currentDate.getTime()))
        }

        // Move to the next day
        currentDate.setDate(currentDate.getDate() + 1)
      }

      setDisabledDates(disabledDatesArray)
    } catch (error) {
      console.error("Error loading barber working hours:", error)
      toast.error("Berber çalışma saatleri yüklenirken bir hata oluştu.")
    }
  }

  // Load available times for a specific date and barber
  const loadAvailableTimes = async (date: Date, barberId: string) => {
    try {
      const formattedDate = format(date, "yyyy-MM-dd")
      const dayOfWeek = date.getDay() // 0 = Sunday, 6 = Saturday

      // Check if the selected date is a holiday
      const holidaysData = await publicAccess.getPublicHolidaysBySalonId(salonId)
      const holidayData = holidaysData?.find(h => h.date === formattedDate)

      // If the selected date is a holiday, show a message and return
      if (holidayData) {
        setAvailableTimes([])
        const holidayDescription = holidayData.description ? ` (${holidayData.description})` : ''
        toast.error(`Seçilen tarih tatil günüdür${holidayDescription}. Lütfen başka bir tarih seçin.`)
        return
      }

      // Get barber working hours for the selected day from the already loaded data
      const workingHoursData = barberWorkingHoursList.find(wh =>
        wh.barber_id === barberId && wh.day_of_week === dayOfWeek
      )

      // Check if barber is working on the selected day
      if (!workingHoursData || workingHoursData.is_closed) {
        setAvailableTimes([])
        toast.error("Seçilen berber bu gün çalışmıyor. Lütfen başka bir gün veya berber seçin.")
        return
      }

      // Get existing appointments for the selected date and barber
      const existingAppointments = await publicAccess.getPublicAppointmentsBySalonId(
        salonId,
        formattedDate,
        formattedDate
      )

      // Set existing appointment times for the TimeSlotGrid component
      const existingTimes = (existingAppointments || []).map(apt => apt.start_time.substring(0, 5))
      setExistingAppointmentTimes(existingTimes)
      console.log("loadAvailableTimes - existingTimes:", existingTimes)

      // If we don't have a selected service, we can't calculate available times
      if (!selectedService) {
        setAvailableTimes([])
        toast.error("Lütfen önce bir hizmet seçin.")
        return
      }

      // Generate available time slots based on barber's working hours
      const timeSlots = []

      // Parse working hours
      const openTime = workingHoursData.open_time.substring(0, 5)
      const closeTime = workingHoursData.close_time.substring(0, 5)

      const [openHour, openMinute] = openTime.split(':').map(Number)
      const [closeHour, closeMinute] = closeTime.split(':').map(Number)

      // Generate time slots at 30-minute intervals within working hours
      let currentHour = openHour
      let currentMinute = openMinute

      while (
        currentHour < closeHour ||
        (currentHour === closeHour && currentMinute < closeMinute)
      ) {
        const timeSlot = `${currentHour.toString().padStart(2, '0')}:${currentMinute.toString().padStart(2, '0')}`
        timeSlots.push(timeSlot)

        // Move to next 30-minute slot
        currentMinute += 30
        if (currentMinute >= 60) {
          currentHour += 1
          currentMinute = 0
        }
      }

      // Filter out times that conflict with existing appointments
      const availableSlots = timeSlots.filter(timeSlot => {
        // We already checked for selectedService above
        const startTime = parse(timeSlot, "HH:mm", new Date())
        const endTime = addMinutes(startTime, selectedService.duration)
        const endTimeString = format(endTime, "HH:mm")

        // Skip if the appointment would end after closing time
        if (endTimeString > closeTime) return false

        // Check for lunch break if applicable
        if (workingHoursData.has_lunch_break && workingHoursData.lunch_start_time && workingHoursData.lunch_end_time) {
          const lunchStart = workingHoursData.lunch_start_time.substring(0, 5)
          const lunchEnd = workingHoursData.lunch_end_time.substring(0, 5)

          // Skip if appointment overlaps with lunch break
          if (timeSlot < lunchEnd && endTimeString > lunchStart) {
            return false
          }
        }

        // Check for conflicts with existing appointments
        for (const appointment of (existingAppointments || [])) {
          const appointmentStart = appointment.start_time.substring(0, 5)
          const appointmentEnd = appointment.end_time.substring(0, 5)

          // Skip if there's an overlap
          if (timeSlot < appointmentEnd && endTimeString > appointmentStart) {
            return false
          }
        }

        return true
      })

      setAvailableTimes(availableSlots)
      console.log("loadAvailableTimes - availableSlots:", availableSlots)

      // If no available slots, show a message
      if (availableSlots.length === 0) {
        toast.error("Seçilen tarih ve berber için müsait saat bulunmamaktadır. Lütfen başka bir tarih veya berber seçin.")
      }
    } catch (error) {
      console.error("Error loading available times:", error)
      toast.error("Müsait saatler yüklenirken bir hata oluştu.")
    }
  }

  // Generate OTP for phone verification
  const generateOTP = async (phoneNumber: string) => {
    setIsOtpLoading(true)
    setOtpError(null)

    try {
      const response = await fetch('/api/sms/verify-otp', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'generate',
          salonId,
          phoneNumber,
          purpose: 'booking_verification',
          contextData: {
            customerName: form.getValues("name") + ' ' + form.getValues("surname"),
            serviceName: selectedService?.name,
            barberName: barbersList.find(b => b.id === selectedBarber)?.name,
            appointmentDate: selectedDate ? format(selectedDate, "yyyy-MM-dd") : null,
            appointmentTime: form.getValues("start_time")
          }
        }),
      })

      const result = await response.json()

      if (result.success) {
        setOtpId(result.otpId)
        setOtpExpiryTime(new Date(result.expiresAt))
        toast.success("Doğrulama kodu gönderildi")
      } else {
        setOtpError(result.message || "OTP gönderilemedi")
        toast.error(result.message || "Doğrulama kodu gönderilemedi")
      }
    } catch (error) {
      console.error('OTP generation error:', error)
      setOtpError("Sistem hatası oluştu")
      toast.error("Doğrulama kodu gönderilirken hata oluştu")
    } finally {
      setIsOtpLoading(false)
    }
  }

  // Verify OTP code
  const verifyOTP = async (otpCode: string) => {
    setIsOtpVerifying(true)
    setOtpError(null)

    try {
      const response = await fetch('/api/sms/verify-otp', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'verify',
          salonId,
          phoneNumber: form.getValues("phone"),
          otpCode,
          otpId
        }),
      })

      const result = await response.json()

      if (result.success) {
        setIsOtpVerified(true)
        toast.success("Telefon numarası doğrulandı")
        setCurrentStep("summary")
      } else {
        setOtpError(result.message || "Geçersiz doğrulama kodu")
        toast.error(result.message || "Geçersiz doğrulama kodu")
      }
    } catch (error) {
      console.error('OTP verification error:', error)
      setOtpError("Sistem hatası oluştu")
      toast.error("Doğrulama sırasında hata oluştu")
    } finally {
      setIsOtpVerifying(false)
    }
  }

  // Handle OTP resend
  const handleOtpResend = async () => {
    const phoneNumber = form.getValues("phone")
    if (phoneNumber) {
      await generateOTP(phoneNumber)
    }
  }

  // Handle OTP expiry
  const handleOtpExpired = () => {
    setOtpError("Doğrulama kodunun süresi doldu")
    toast.error("Doğrulama kodunun süresi doldu. Yeni kod talep edin.")
  }

  // Handle form submission
  const onSubmit = async () => {
    setIsLoading(true)

    // Check if OTP is verified
    if (!isOtpVerified) {
      toast.error("Lütfen telefon numaranızı doğrulayın")
      setIsLoading(false)
      return
    }

    try {
      const values = form.getValues()

      if (!selectedDate) {
        toast.error("Lütfen bir tarih seçin.")
        setIsLoading(false)
        return
      }

      const formattedDate = format(selectedDate, "yyyy-MM-dd")

      if (!selectedService) {
        toast.error("Lütfen bir hizmet seçin.")
        setIsLoading(false)
        return
      }

      if (!values.start_time) {
        toast.error("Lütfen bir saat seçin.")
        setIsLoading(false)
        return
      }

      // Calculate end time
      const startTime = parse(values.start_time, "HH:mm", new Date())
      const endTime = addMinutes(startTime, selectedService.duration)
      const endTimeString = format(endTime, "HH:mm")

      // Appointment data
      const appointmentData = {
        salon_id: salonId,
        date: formattedDate,
        start_time: values.start_time,
        end_time: endTimeString,
        barber_id: selectedBarber as string,
        service_id: selectedService.id,
        fullname: values.name + ' ' + values.surname,
        phonenumber: values.phone,
        email: values.email || undefined,
        status: "booked" as const
        // Fiyat bilgisi gönderilmiyor
      }

      // Create appointment
      const newAppointmentId = await publicAccess.createPublicAppointment(
        salonId,
        selectedBarber as string,
        selectedService.id,
        formattedDate,
        values.start_time,
        endTimeString,
        values.name + ' ' + values.surname,
        values.phone,
        values.email || ''
      )
      toast.success("Randevu başarıyla oluşturuldu!")

      // Generate cancellation token and send confirmation SMS (non-blocking)
      try {
        const tokenResult = await generateAppointmentCancellationToken(newAppointmentId, salonId)

        if (tokenResult.success && tokenResult.cancelLink) {
          // Send appointment confirmation SMS with cancellation link
          const smsResponse = await fetch('/api/sms/send', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              type: 'appointment_confirmation',
              salonId,
              recipientPhone: values.phone,
              appointmentId: newAppointmentId,
              templateData: {
                salon_name: '', // Will be fetched by the API
                customer_name: values.name + ' ' + values.surname,
                date: new Date(formattedDate).toLocaleDateString('tr-TR'),
                time: values.start_time,
                service_name: selectedService.name,
                barber_name: barbersList.find(b => b.id === selectedBarber)?.name || '',
                cancel_link: tokenResult.cancelLink
              }
            }),
          })

          const smsResult = await smsResponse.json()
          if (smsResult.success) {
            console.log('Confirmation SMS sent successfully')
          } else {
            console.error('Failed to send confirmation SMS:', smsResult.message)
          }
        } else {
          console.error('Failed to generate cancellation token:', tokenResult.message)
        }
      } catch (error) {
        console.error('Error sending confirmation SMS:', error)
        // Don't show error to user as this is a background operation
      }

      // Send Telegram notification for new appointment (non-blocking)
      try {
        const selectedBarberData = barbersList.find(b => b.id === selectedBarber)

        if (selectedBarberData && selectedService && newAppointmentId) {
          const notificationData = formatAppointmentForNotification(
            {
              id: newAppointmentId,
              salon_id: salonId,
              salon_name: '', // salon name will be fetched in the API
              fullname: values.name + ' ' + values.surname,
              phonenumber: values.phone,
              email: values.email || '',
              barber_name: selectedBarberData.name,
              service_name: selectedService.name,
              date: formattedDate,
              start_time: values.start_time,
              end_time: endTimeString,
              status: 'booked',
              notes: ''
            },
            '', // salon name will be fetched in the API
            selectedBarberData.name,
            selectedService.name
          )

          notifyNewAppointment(notificationData)
        }
      } catch (error) {
        console.error('Failed to send Telegram notification for new appointment:', error)
        // Don't show error to user as this is a background operation
      }

      // Call onSuccess callback if provided
      if (onSuccess) {
        onSuccess()
      }
    } catch (error) {
      console.error("Error creating appointment:", error)
      toast.error("Randevu oluşturulurken bir hata oluştu.")
    } finally {
      setIsLoading(false)
    }
  }

  // Navigate to previous step
  const goToPreviousStep = () => {
    switch (currentStep) {
      case "barber":
        setCurrentStep("service")
        break
      case "date":
        setCurrentStep("barber")
        break
      case "time":
        setCurrentStep("date")
        break
      case "info":
        setCurrentStep("time")
        break
      case "otp":
        setCurrentStep("info")
        break
      case "summary":
        setCurrentStep("otp")
        break
      default:
        break
    }
  }

  return (
    <Form {...form}>
      <div className="flex flex-col min-h-[600px]">
        <StepIndicator
          currentStep={currentStep}
          steps={["service", "barber", "date", "time", "info", "otp", "summary"]}
        />

        <div className="flex-1 p-6 sm:p-8 bg-gradient-to-b from-background to-muted/20">
          {currentStep === "service" && (
            <ServiceSelector
              services={servicesList}
              selectedServiceId={form.getValues("service_id")}
              onSelect={handleServiceChange}
            />
          )}

          {currentStep === "barber" && (
            <BarberSelector
              barbers={barbersList}
              selectedBarberId={form.getValues("barber_id")}
              onSelect={handleBarberChange}
              onBack={goToPreviousStep}
            />
          )}

          {currentStep === "date" && (
            <DateSelector
              selectedDate={selectedDate}
              onSelect={handleDateChange}
              disabledDates={disabledDates}
              holidayDates={holidayDates}
              onBack={goToPreviousStep}
            />
          )}

          {currentStep === "time" && (
            <TimeSelector
              date={selectedDate}
              availableTimes={availableTimes}
              selectedTime={form.getValues("start_time")}
              onSelect={handleTimeChange}
              barberWorkingHours={barberWorkingHoursList}
              existingAppointmentTimes={existingAppointmentTimes}
              onBack={goToPreviousStep}
            />
          )}

          {currentStep === "info" && (
            <CustomerInfoForm
              defaultValues={{
                name: form.getValues("name"),
                surname: form.getValues("surname"),
                phone: form.getValues("phone"),
                email: form.getValues("email") || ""
              }}
              onSubmit={handleCustomerInfoSubmit}
              onBack={goToPreviousStep}
            />
          )}

          {currentStep === "otp" && (
            <div className="space-y-6">
              <div className="text-center space-y-2">
                <h3 className="text-lg font-semibold">Telefon Doğrulama</h3>
                <p className="text-muted-foreground">
                  {form.getValues("phone")} numarasına gönderilen doğrulama kodunu girin
                </p>
              </div>

              <div className="max-w-md mx-auto">
                <OTPInputWithTimer
                  length={6}
                  value={otpCode}
                  onChange={setOtpCode}
                  onComplete={verifyOTP}
                  disabled={isOtpVerifying}
                  error={!!otpError}
                  errorMessage={otpError || undefined}
                  helperText={!otpError ? "SMS ile gönderilen 6 haneli kodu girin" : undefined}
                  expiryTime={otpExpiryTime || undefined}
                  onExpired={handleOtpExpired}
                  onResend={handleOtpResend}
                  resendDisabled={isOtpLoading}
                  autoFocus
                />
              </div>

              <div className="flex justify-between">
                <Button
                  type="button"
                  variant="outline"
                  onClick={goToPreviousStep}
                  disabled={isOtpVerifying || isOtpLoading}
                >
                  Geri
                </Button>

                <Button
                  type="button"
                  onClick={() => verifyOTP(otpCode)}
                  disabled={otpCode.length !== 6 || isOtpVerifying || !otpCode}
                  className="min-w-[120px]"
                >
                  {isOtpVerifying ? "Doğrulanıyor..." : "Doğrula"}
                </Button>
              </div>
            </div>
          )}

          {currentStep === "summary" && (
            <BookingSummary
              service={selectedService}
              barber={barbersList.find(b => b.id === selectedBarber)}
              date={selectedDate}
              time={form.getValues("start_time") || ""}
              customerInfo={{
                name: form.getValues("name"),
                surname: form.getValues("surname"),
                phone: form.getValues("phone"),
                email: form.getValues("email") || ""
              }}
              onSubmit={onSubmit}
              onBack={goToPreviousStep}
              isLoading={isLoading}
            />
          )}
        </div>
      </div>
    </Form>
  )
}
