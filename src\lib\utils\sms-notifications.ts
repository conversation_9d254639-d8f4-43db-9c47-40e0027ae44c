/**
 * SMS Notifications Utility
 * Handles SMS notifications for appointments, OTP verification, and other events
 * Follows the same pattern as telegram-notifications.ts
 */

import { NetGSMService, createNetGSMService } from '@/lib/services/netgsm';
import { otpService } from '@/lib/services/otp';
import { createClient } from '@supabase/supabase-js';

// SMS Notification Types
export type SMSNotificationType = 
  | 'otp_verification'
  | 'appointment_confirmation'
  | 'appointment_cancelled'
  | 'appointment_updated'
  | 'appointment_reminder';

// SMS Notification Data Interface
export interface SMSNotificationData {
  type: SMSNotificationType;
  salonId: string;
  recipientPhone: string;
  appointmentId?: string;
  otpVerificationId?: string;
  templateData: {
    [key: string]: string | number | Date;
  };
}

// SMS Sending Result
export interface SMSSendResult {
  success: boolean;
  jobId?: string;
  error?: string;
  message?: string;
  deliveryLogId?: string;
}

// SMS Template Data for different notification types
interface AppointmentTemplateData {
  salon_name: string;
  customer_name: string;
  date: string;
  time: string;
  service_name: string;
  barber_name?: string;
  cancel_link?: string;
}

interface OTPTemplateData {
  otp_code: string;
  salon_name?: string;
  expiry_minutes: number;
}

/**
 * SMS Notifications Service Class
 */
export class SMSNotificationsService {
  private supabase;

  constructor() {
    this.supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );
  }

  /**
   * Send SMS notification
   * @param data SMS notification data
   * @returns SMS sending result
   */
  async sendSMSNotification(data: SMSNotificationData): Promise<SMSSendResult> {
    try {
      // 1. Get salon SMS settings
      const smsSettings = await this.getSalonSMSSettings(data.salonId);
      if (!smsSettings) {
        return {
          success: false,
          error: 'sms_not_configured',
          message: 'SMS ayarları yapılandırılmamış'
        };
      }

      // 2. Check if SMS is enabled for salon
      if (!smsSettings.is_enabled) {
        return {
          success: false,
          error: 'sms_disabled',
          message: 'SMS bildirimleri devre dışı'
        };
      }

      // 3. Create NetGSM service instance
      const netgsmService = await this.createNetGSMServiceForSalon(smsSettings);
      if (!netgsmService) {
        return {
          success: false,
          error: 'netgsm_config_error',
          message: 'NetGSM yapılandırma hatası'
        };
      }

      // 4. Generate SMS message from template
      const message = await this.generateSMSMessage(data, smsSettings);
      if (!message) {
        return {
          success: false,
          error: 'template_error',
          message: 'SMS mesajı oluşturulamadı'
        };
      }

      // 5. Validate phone number
      const formattedPhone = NetGSMService.formatTurkishPhone(data.recipientPhone);
      if (!this.isValidTurkishPhone(formattedPhone)) {
        return {
          success: false,
          error: 'invalid_phone',
          message: 'Geçersiz telefon numarası'
        };
      }

      // 6. Check rate limits
      const rateLimitCheck = await this.checkRateLimit(data.salonId, data.type);
      if (!rateLimitCheck.allowed) {
        return {
          success: false,
          error: 'rate_limited',
          message: 'SMS gönderim limiti aşıldı'
        };
      }

      // 7. Send SMS via NetGSM
      const sendResult = await netgsmService.sendSMS(formattedPhone, message);

      // 8. Log SMS delivery attempt
      const deliveryLogId = await this.logSMSDelivery({
        salonId: data.salonId,
        recipientPhone: formattedPhone,
        messageContent: message,
        messageType: data.type,
        netgsmJobId: sendResult.jobId,
        deliveryStatus: sendResult.success ? 'sent' : 'failed',
        errorCode: sendResult.error,
        errorMessage: sendResult.message,
        appointmentId: data.appointmentId,
        otpVerificationId: data.otpVerificationId
      });

      // 9. Update rate limit counters
      if (sendResult.success) {
        await this.updateRateLimitCounters(data.salonId);
      }

      return {
        success: sendResult.success,
        jobId: sendResult.jobId,
        error: sendResult.error,
        message: sendResult.message,
        deliveryLogId
      };

    } catch (error) {
      console.error('SMS notification error:', error);
      return {
        success: false,
        error: 'system_error',
        message: 'SMS gönderimi sırasında sistem hatası oluştu'
      };
    }
  }

  /**
   * Send OTP verification SMS
   */
  async sendOTPVerificationSMS(
    salonId: string,
    phoneNumber: string,
    otpCode: string,
    salonName?: string
  ): Promise<SMSSendResult> {
    const templateData: OTPTemplateData = {
      otp_code: otpCode,
      salon_name: salonName,
      expiry_minutes: 5
    };

    return this.sendSMSNotification({
      type: 'otp_verification',
      salonId,
      recipientPhone: phoneNumber,
      templateData
    });
  }

  /**
   * Send appointment confirmation SMS
   */
  async sendAppointmentConfirmationSMS(
    salonId: string,
    appointmentId: string,
    appointmentData: AppointmentTemplateData
  ): Promise<SMSSendResult> {
    return this.sendSMSNotification({
      type: 'appointment_confirmation',
      salonId,
      recipientPhone: appointmentData.customer_name, // This should be phone number
      appointmentId,
      templateData: appointmentData
    });
  }

  /**
   * Get salon SMS settings from database
   */
  private async getSalonSMSSettings(salonId: string) {
    try {
      const { data, error } = await this.supabase
        .from('salon_sms_settings')
        .select('*')
        .eq('salon_id', salonId)
        .single();

      if (error && error.code !== 'PGRST116') { // Not found error
        console.error('Error fetching salon SMS settings:', error);
        return null;
      }

      // If no salon-specific settings, check if we should use system default
      if (!data) {
        const { data: systemConfig } = await this.supabase
          .from('system_sms_config')
          .select('*')
          .single();

        if (systemConfig && systemConfig.is_sms_enabled) {
          // Return a default configuration using system settings
          return {
            salon_id: salonId,
            is_enabled: true,
            use_system_default: true,
            netgsm_username_encrypted: systemConfig.default_netgsm_username_encrypted,
            netgsm_password_encrypted: systemConfig.default_netgsm_password_encrypted,
            netgsm_header: systemConfig.default_netgsm_header,
            otp_template: 'SalonFlow doğrulama kodunuz: {otp_code}. Bu kod {expiry_minutes} dakika geçerlidir.',
            appointment_confirmation_template: 'Randevunuz onaylandı! {salon_name} - {date} {time} - {service_name}. İptal: {cancel_link}',
            appointment_cancelled_template: 'Randevunuz iptal edildi. {salon_name} - {date} {time} - {service_name}',
            appointment_updated_template: 'Randevunuz güncellendi. {salon_name} - Yeni tarih: {date} {time} - {service_name}',
            daily_sms_limit: 1000,
            hourly_sms_limit: 100
          };
        }
      }

      return data;
    } catch (error) {
      console.error('Error in getSalonSMSSettings:', error);
      return null;
    }
  }

  /**
   * Create NetGSM service instance for salon
   */
  private async createNetGSMServiceForSalon(smsSettings: any): Promise<NetGSMService | null> {
    try {
      if (smsSettings.use_system_default) {
        // Use system default credentials
        return await createNetGSMService(
          smsSettings.netgsm_username_encrypted,
          smsSettings.netgsm_password_encrypted,
          smsSettings.netgsm_header,
          'SalonFlow'
        );
      } else {
        // Use salon-specific credentials
        return await createNetGSMService(
          smsSettings.netgsm_username_encrypted,
          smsSettings.netgsm_password_encrypted,
          smsSettings.netgsm_header,
          'SalonFlow'
        );
      }
    } catch (error) {
      console.error('Error creating NetGSM service:', error);
      return null;
    }
  }

  /**
   * Generate SMS message from template
   */
  private async generateSMSMessage(
    data: SMSNotificationData,
    smsSettings: any
  ): Promise<string | null> {
    try {
      let template = '';

      // Get appropriate template based on notification type
      switch (data.type) {
        case 'otp_verification':
          template = smsSettings.otp_template || 'SalonFlow doğrulama kodunuz: {otp_code}. Bu kod {expiry_minutes} dakika geçerlidir.';
          break;
        case 'appointment_confirmation':
          template = smsSettings.appointment_confirmation_template || 'Randevunuz onaylandı! {salon_name} - {date} {time} - {service_name}. İptal: {cancel_link}';
          break;
        case 'appointment_cancelled':
          template = smsSettings.appointment_cancelled_template || 'Randevunuz iptal edildi. {salon_name} - {date} {time} - {service_name}';
          break;
        case 'appointment_updated':
          template = smsSettings.appointment_updated_template || 'Randevunuz güncellendi. {salon_name} - Yeni tarih: {date} {time} - {service_name}';
          break;
        default:
          return null;
      }

      // Replace template variables with actual data
      let message = template;
      for (const [key, value] of Object.entries(data.templateData)) {
        const placeholder = `{${key}}`;
        message = message.replace(new RegExp(placeholder, 'g'), String(value));
      }

      return message;
    } catch (error) {
      console.error('Error generating SMS message:', error);
      return null;
    }
  }

  /**
   * Check rate limits for SMS sending
   */
  private async checkRateLimit(salonId: string, messageType: SMSNotificationType) {
    try {
      const { data: settings } = await this.supabase
        .from('salon_sms_settings')
        .select('daily_sms_sent, hourly_sms_sent, daily_sms_limit, hourly_sms_limit, last_daily_reset, last_hourly_reset')
        .eq('salon_id', salonId)
        .single();

      if (!settings) {
        return { allowed: true }; // Allow if no settings found
      }

      const now = new Date();
      const lastDailyReset = new Date(settings.last_daily_reset);
      const lastHourlyReset = new Date(settings.last_hourly_reset);

      // Check if we need to reset counters
      const shouldResetDaily = now.getDate() !== lastDailyReset.getDate() || 
                              now.getMonth() !== lastDailyReset.getMonth() ||
                              now.getFullYear() !== lastDailyReset.getFullYear();

      const shouldResetHourly = now.getTime() - lastHourlyReset.getTime() >= 60 * 60 * 1000; // 1 hour

      let dailySent = settings.daily_sms_sent;
      let hourlySent = settings.hourly_sms_sent;

      if (shouldResetDaily) dailySent = 0;
      if (shouldResetHourly) hourlySent = 0;

      // Check limits
      const dailyLimitExceeded = dailySent >= settings.daily_sms_limit;
      const hourlyLimitExceeded = hourlySent >= settings.hourly_sms_limit;

      return {
        allowed: !dailyLimitExceeded && !hourlyLimitExceeded,
        dailyLimitExceeded,
        hourlyLimitExceeded,
        dailyRemaining: Math.max(0, settings.daily_sms_limit - dailySent),
        hourlyRemaining: Math.max(0, settings.hourly_sms_limit - hourlySent)
      };

    } catch (error) {
      console.error('Error checking rate limit:', error);
      return { allowed: true }; // Allow on error to prevent blocking
    }
  }

  /**
   * Update rate limit counters after successful SMS send
   */
  private async updateRateLimitCounters(salonId: string) {
    try {
      const now = new Date();
      
      await this.supabase
        .from('salon_sms_settings')
        .update({
          daily_sms_sent: this.supabase.raw('daily_sms_sent + 1'),
          hourly_sms_sent: this.supabase.raw('hourly_sms_sent + 1'),
          last_daily_reset: now.toISOString(),
          last_hourly_reset: now.toISOString()
        })
        .eq('salon_id', salonId);

    } catch (error) {
      console.error('Error updating rate limit counters:', error);
    }
  }

  /**
   * Log SMS delivery attempt to database
   */
  private async logSMSDelivery(logData: {
    salonId: string;
    recipientPhone: string;
    messageContent: string;
    messageType: string;
    netgsmJobId?: string;
    deliveryStatus: string;
    errorCode?: string;
    errorMessage?: string;
    appointmentId?: string;
    otpVerificationId?: string;
  }): Promise<string | null> {
    try {
      const { data, error } = await this.supabase
        .from('sms_delivery_log')
        .insert({
          salon_id: logData.salonId,
          recipient_phone: logData.recipientPhone,
          message_content: logData.messageContent,
          message_type: logData.messageType,
          netgsm_job_id: logData.netgsmJobId,
          delivery_status: logData.deliveryStatus,
          error_code: logData.errorCode,
          error_message: logData.errorMessage,
          appointment_id: logData.appointmentId,
          otp_verification_id: logData.otpVerificationId,
          sent_at: logData.deliveryStatus === 'sent' ? new Date().toISOString() : null,
          failed_at: logData.deliveryStatus === 'failed' ? new Date().toISOString() : null
        })
        .select('id')
        .single();

      if (error) {
        console.error('Error logging SMS delivery:', error);
        return null;
      }

      return data?.id || null;
    } catch (error) {
      console.error('Error in logSMSDelivery:', error);
      return null;
    }
  }

  /**
   * Validate Turkish phone number
   */
  private isValidTurkishPhone(phone: string): boolean {
    const phoneRegex = /^5[0-9]{9}$/;
    return phoneRegex.test(phone);
  }
}

// Export singleton instance
export const smsNotificationsService = new SMSNotificationsService();

// Export helper functions for easy use
export async function sendOTPSMS(
  salonId: string,
  phoneNumber: string,
  otpCode: string,
  salonName?: string
): Promise<SMSSendResult> {
  return smsNotificationsService.sendOTPVerificationSMS(salonId, phoneNumber, otpCode, salonName);
}

export async function sendAppointmentConfirmationSMS(
  salonId: string,
  appointmentId: string,
  appointmentData: AppointmentTemplateData
): Promise<SMSSendResult> {
  return smsNotificationsService.sendAppointmentConfirmationSMS(salonId, appointmentId, appointmentData);
}
