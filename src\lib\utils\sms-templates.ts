/**
 * SMS Message Templates (Turkish)
 * Provides standardized SMS message templates for different notification types
 */

// Template variable types
export interface OTPTemplateVars {
  otp_code: string;
  salon_name?: string;
  expiry_minutes: number;
}

export interface AppointmentTemplateVars {
  salon_name: string;
  customer_name: string;
  date: string;
  time: string;
  service_name: string;
  barber_name?: string;
  cancel_link?: string;
  old_date?: string;
  old_time?: string;
}

export interface ReminderTemplateVars {
  salon_name: string;
  customer_name: string;
  date: string;
  time: string;
  service_name: string;
  barber_name?: string;
  salon_address?: string;
  salon_phone?: string;
}

/**
 * SMS Template Categories
 */
export const SMS_TEMPLATES = {
  // OTP Verification Templates
  OTP: {
    BOOKING_VERIFICATION: 'SalonFlow doğrulama kodunuz: {otp_code}. Bu kod {expiry_minutes} dakika geçerlidir. Kodu kimseyle paylaşmayın.',
    
    BOOKING_VERIFICATION_WITH_SALON: '{salon_name} randevu doğrulama kodunuz: {otp_code}. Bu kod {expiry_minutes} dakika geçerlidir.',
    
    PASSWORD_RESET: 'SalonFlow şifre sıfırlama kodunuz: {otp_code}. Bu kod {expiry_minutes} dakika geçerlidir.',
    
    PHONE_VERIFICATION: 'Telefon numarası doğrulama kodunuz: {otp_code}. Bu kod {expiry_minutes} dakika geçerlidir.'
  },

  // Appointment Confirmation Templates
  APPOINTMENT_CONFIRMATION: {
    BASIC: 'Randevunuz onaylandı! {salon_name} - {date} {time} - {service_name}',
    
    WITH_BARBER: 'Randevunuz onaylandı! {salon_name} - {date} {time} - {service_name} ({barber_name})',
    
    WITH_CANCELLATION: 'Randevunuz onaylandı! {salon_name} - {date} {time} - {service_name}. İptal için: {cancel_link}',
    
    FULL: 'Merhaba {customer_name}, randevunuz onaylandı! {salon_name} - {date} {time} - {service_name} ({barber_name}). İptal için: {cancel_link}'
  },

  // Appointment Cancellation Templates
  APPOINTMENT_CANCELLED: {
    BASIC: 'Randevunuz iptal edildi. {salon_name} - {date} {time} - {service_name}',
    
    WITH_REASON: 'Randevunuz iptal edildi. {salon_name} - {date} {time} - {service_name}. Yeni randevu için lütfen iletişime geçin.',
    
    BY_CUSTOMER: 'Randevunuz başarıyla iptal edildi. {salon_name} - {date} {time} - {service_name}. Teşekkür ederiz.',
    
    BY_SALON: 'Üzgünüz, randevunuz iptal edilmek zorunda kaldı. {salon_name} - {date} {time} - {service_name}. Yeni randevu için lütfen arayın.'
  },

  // Appointment Update Templates
  APPOINTMENT_UPDATED: {
    BASIC: 'Randevunuz güncellendi. {salon_name} - Yeni tarih: {date} {time} - {service_name}',
    
    WITH_OLD_DATE: 'Randevunuz güncellendi. {salon_name} - Eski: {old_date} {old_time}, Yeni: {date} {time} - {service_name}',
    
    WITH_BARBER: 'Randevunuz güncellendi. {salon_name} - {date} {time} - {service_name} ({barber_name})',
    
    FULL: 'Merhaba {customer_name}, randevunuz güncellendi. {salon_name} - Yeni tarih: {date} {time} - {service_name} ({barber_name})'
  },

  // Appointment Reminder Templates
  APPOINTMENT_REMINDER: {
    ONE_DAY: 'Yarın randevunuz var! {salon_name} - {date} {time} - {service_name}. Adres: {salon_address}',
    
    ONE_HOUR: 'Randevunuz 1 saat sonra! {salon_name} - {time} - {service_name} ({barber_name})',
    
    THIRTY_MINUTES: 'Randevunuz 30 dakika sonra! {salon_name} - {time} - {service_name}. Lütfen zamanında gelin.',
    
    WITH_CONTACT: 'Yarın randevunuz var! {salon_name} - {date} {time} - {service_name}. İletişim: {salon_phone}'
  },

  // Special Occasion Templates
  SPECIAL: {
    WELCOME: 'Hoş geldiniz! {salon_name} ailesine katıldığınız için teşekkür ederiz. İlk randevunuz için özel indirim!',
    
    BIRTHDAY: 'Doğum gününüz kutlu olsun! {salon_name} olarak size özel bir hediyemiz var. Randevu alın!',
    
    LOYALTY: 'Sadık müşterimiz {customer_name}, {salon_name} olarak size özel indirim! Randevu için arayın.',
    
    FEEDBACK: 'Hizmetimizden memnun kaldınız mı? {salon_name} olarak görüşleriniz bizim için değerli.'
  }
};

/**
 * Template Processing Functions
 */
export class SMSTemplateProcessor {
  /**
   * Process template with variables
   * @param template Template string with placeholders
   * @param variables Object containing variable values
   * @returns Processed message string
   */
  static processTemplate(template: string, variables: Record<string, any>): string {
    let processedMessage = template;

    // Replace all template variables
    for (const [key, value] of Object.entries(variables)) {
      const placeholder = `{${key}}`;
      const regex = new RegExp(placeholder.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g');
      processedMessage = processedMessage.replace(regex, String(value || ''));
    }

    // Clean up any remaining unreplaced placeholders
    processedMessage = processedMessage.replace(/\{[^}]+\}/g, '');

    // Clean up extra spaces
    processedMessage = processedMessage.replace(/\s+/g, ' ').trim();

    return processedMessage;
  }

  /**
   * Get OTP verification message
   */
  static getOTPMessage(variables: OTPTemplateVars): string {
    const template = variables.salon_name 
      ? SMS_TEMPLATES.OTP.BOOKING_VERIFICATION_WITH_SALON
      : SMS_TEMPLATES.OTP.BOOKING_VERIFICATION;
    
    return this.processTemplate(template, variables);
  }

  /**
   * Get appointment confirmation message
   */
  static getAppointmentConfirmationMessage(variables: AppointmentTemplateVars): string {
    let template = SMS_TEMPLATES.APPOINTMENT_CONFIRMATION.BASIC;

    // Choose template based on available data
    if (variables.cancel_link && variables.barber_name) {
      template = SMS_TEMPLATES.APPOINTMENT_CONFIRMATION.FULL;
    } else if (variables.cancel_link) {
      template = SMS_TEMPLATES.APPOINTMENT_CONFIRMATION.WITH_CANCELLATION;
    } else if (variables.barber_name) {
      template = SMS_TEMPLATES.APPOINTMENT_CONFIRMATION.WITH_BARBER;
    }

    return this.processTemplate(template, variables);
  }

  /**
   * Get appointment cancellation message
   */
  static getAppointmentCancellationMessage(
    variables: AppointmentTemplateVars, 
    cancelledBy: 'customer' | 'salon' = 'salon'
  ): string {
    const template = cancelledBy === 'customer' 
      ? SMS_TEMPLATES.APPOINTMENT_CANCELLED.BY_CUSTOMER
      : SMS_TEMPLATES.APPOINTMENT_CANCELLED.BY_SALON;
    
    return this.processTemplate(template, variables);
  }

  /**
   * Get appointment update message
   */
  static getAppointmentUpdateMessage(variables: AppointmentTemplateVars): string {
    let template = SMS_TEMPLATES.APPOINTMENT_UPDATED.BASIC;

    // Choose template based on available data
    if (variables.old_date && variables.old_time && variables.barber_name) {
      template = SMS_TEMPLATES.APPOINTMENT_UPDATED.FULL;
    } else if (variables.old_date && variables.old_time) {
      template = SMS_TEMPLATES.APPOINTMENT_UPDATED.WITH_OLD_DATE;
    } else if (variables.barber_name) {
      template = SMS_TEMPLATES.APPOINTMENT_UPDATED.WITH_BARBER;
    }

    return this.processTemplate(template, variables);
  }

  /**
   * Get appointment reminder message
   */
  static getAppointmentReminderMessage(
    variables: ReminderTemplateVars, 
    reminderType: 'one_day' | 'one_hour' | 'thirty_minutes' = 'one_day'
  ): string {
    let template = SMS_TEMPLATES.APPOINTMENT_REMINDER.ONE_DAY;

    switch (reminderType) {
      case 'one_hour':
        template = SMS_TEMPLATES.APPOINTMENT_REMINDER.ONE_HOUR;
        break;
      case 'thirty_minutes':
        template = SMS_TEMPLATES.APPOINTMENT_REMINDER.THIRTY_MINUTES;
        break;
      case 'one_day':
        template = variables.salon_phone 
          ? SMS_TEMPLATES.APPOINTMENT_REMINDER.WITH_CONTACT
          : SMS_TEMPLATES.APPOINTMENT_REMINDER.ONE_DAY;
        break;
    }

    return this.processTemplate(template, variables);
  }

  /**
   * Validate template variables
   */
  static validateTemplateVariables(
    template: string, 
    variables: Record<string, any>
  ): { valid: boolean; missingVariables: string[] } {
    const placeholderRegex = /\{([^}]+)\}/g;
    const requiredVariables = new Set<string>();
    let match;

    // Extract all placeholders from template
    while ((match = placeholderRegex.exec(template)) !== null) {
      requiredVariables.add(match[1]);
    }

    // Check which variables are missing
    const missingVariables = Array.from(requiredVariables).filter(
      variable => !(variable in variables) || variables[variable] === undefined || variables[variable] === null
    );

    return {
      valid: missingVariables.length === 0,
      missingVariables
    };
  }

  /**
   * Get message length in characters (for SMS length calculation)
   */
  static getMessageLength(message: string): number {
    return message.length;
  }

  /**
   * Check if message exceeds SMS length limit
   */
  static isMessageTooLong(message: string, maxLength: number = 160): boolean {
    return this.getMessageLength(message) > maxLength;
  }

  /**
   * Truncate message to fit SMS length limit
   */
  static truncateMessage(message: string, maxLength: number = 160): string {
    if (message.length <= maxLength) {
      return message;
    }

    // Truncate and add ellipsis
    return message.substring(0, maxLength - 3) + '...';
  }

  /**
   * Format date for Turkish locale
   */
  static formatDateTurkish(date: Date | string): string {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    
    const options: Intl.DateTimeFormatOptions = {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    };

    return dateObj.toLocaleDateString('tr-TR', options);
  }

  /**
   * Format time for Turkish locale
   */
  static formatTimeTurkish(time: string | Date): string {
    if (typeof time === 'string') {
      // Assume time is in HH:mm format
      return time;
    }

    const options: Intl.DateTimeFormatOptions = {
      hour: '2-digit',
      minute: '2-digit',
      hour12: false
    };

    return time.toLocaleTimeString('tr-TR', options);
  }

  /**
   * Get day name in Turkish
   */
  static getDayNameTurkish(date: Date | string): string {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    const dayNames = ['Pazar', 'Pazartesi', 'Salı', 'Çarşamba', 'Perşembe', 'Cuma', 'Cumartesi'];
    return dayNames[dateObj.getDay()];
  }

  /**
   * Get month name in Turkish
   */
  static getMonthNameTurkish(date: Date | string): string {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    const monthNames = [
      'Ocak', 'Şubat', 'Mart', 'Nisan', 'Mayıs', 'Haziran',
      'Temmuz', 'Ağustos', 'Eylül', 'Ekim', 'Kasım', 'Aralık'
    ];
    return monthNames[dateObj.getMonth()];
  }
}

// Export default templates for easy access
export default SMS_TEMPLATES;
