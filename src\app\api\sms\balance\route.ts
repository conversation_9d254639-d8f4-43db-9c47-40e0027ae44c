/**
 * SMS Balance API Route
 * Handles NetGSM balance checking for salons
 */

import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { createNetGSMService } from '@/lib/services/netgsm';

/**
 * GET /api/sms/balance
 * Check NetGSM account balance for a salon
 */
export async function GET(request: NextRequest) {
  try {
    const cookieStore = await cookies();
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });

    // Get current user session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    
    if (!session) {
      return NextResponse.json({
        success: false,
        error: 'unauthorized',
        message: 'Bu işlem için giriş yapmanız gerekiyor'
      }, { status: 401 });
    }

    // Get salon ID from query parameters
    const { searchParams } = new URL(request.url);
    const salonId = searchParams.get('salon_id');

    if (!salonId) {
      return NextResponse.json({
        success: false,
        error: 'missing_salon_id',
        message: 'Salon ID gerekli'
      }, { status: 400 });
    }

    // Verify salon access
    const hasAccess = await verifySalonAccess(supabase, session.user.id, salonId);
    if (!hasAccess) {
      return NextResponse.json({
        success: false,
        error: 'forbidden',
        message: 'Bu salona erişim yetkiniz yok'
      }, { status: 403 });
    }

    // Get salon SMS settings
    const { data: smsSettings, error: settingsError } = await supabase
      .from('salon_sms_settings')
      .select('use_system_default, netgsm_username_encrypted, netgsm_password_encrypted, netgsm_header')
      .eq('salon_id', salonId)
      .single();

    if (settingsError) {
      console.error('Error fetching SMS settings:', settingsError);
      return NextResponse.json({
        success: false,
        error: 'settings_not_found',
        message: 'SMS ayarları bulunamadı'
      }, { status: 404 });
    }

    // Determine which credentials to use
    let netgsmService;
    
    if (smsSettings.use_system_default) {
      // Use system default credentials
      const { data: systemConfig, error: systemError } = await supabase
        .from('system_sms_config')
        .select('default_netgsm_username_encrypted, default_netgsm_password_encrypted, default_netgsm_header')
        .single();

      if (systemError || !systemConfig) {
        return NextResponse.json({
          success: false,
          error: 'system_config_not_found',
          message: 'Sistem SMS ayarları bulunamadı'
        }, { status: 500 });
      }

      if (!systemConfig.default_netgsm_username_encrypted || !systemConfig.default_netgsm_password_encrypted) {
        return NextResponse.json({
          success: false,
          error: 'system_credentials_missing',
          message: 'Sistem NetGSM kimlik bilgileri eksik'
        }, { status: 500 });
      }

      try {
        netgsmService = await createNetGSMService(
          systemConfig.default_netgsm_username_encrypted,
          systemConfig.default_netgsm_password_encrypted,
          systemConfig.default_netgsm_header || 'SALONFLOW'
        );
      } catch (error) {
        console.error('Error creating NetGSM service with system credentials:', error);
        return NextResponse.json({
          success: false,
          error: 'netgsm_service_error',
          message: 'NetGSM servisi oluşturulamadı'
        }, { status: 500 });
      }
    } else {
      // Use salon-specific credentials
      if (!smsSettings.netgsm_username_encrypted || !smsSettings.netgsm_password_encrypted) {
        return NextResponse.json({
          success: false,
          error: 'salon_credentials_missing',
          message: 'Salon NetGSM kimlik bilgileri eksik'
        }, { status: 400 });
      }

      try {
        netgsmService = await createNetGSMService(
          smsSettings.netgsm_username_encrypted,
          smsSettings.netgsm_password_encrypted,
          smsSettings.netgsm_header || 'SALONFLOW'
        );
      } catch (error) {
        console.error('Error creating NetGSM service with salon credentials:', error);
        return NextResponse.json({
          success: false,
          error: 'netgsm_service_error',
          message: 'NetGSM servisi oluşturulamadı'
        }, { status: 500 });
      }
    }

    // Check balance
    const balanceResult = await netgsmService.getBalance();

    if (!balanceResult.success) {
      return NextResponse.json({
        success: false,
        error: balanceResult.error,
        message: balanceResult.message || 'Bakiye sorgulanamadı'
      }, { status: 500 });
    }

    // Update balance in database
    try {
      await supabase
        .from('salon_sms_settings')
        .update({
          current_balance: balanceResult.balance,
          last_balance_check: new Date().toISOString()
        })
        .eq('salon_id', salonId);
    } catch (error) {
      console.error('Error updating balance in database:', error);
      // Don't fail the request if database update fails
    }

    return NextResponse.json({
      success: true,
      balance: balanceResult.balance,
      message: 'Bakiye başarıyla sorgulandı'
    });

  } catch (error) {
    console.error('SMS balance API error:', error);
    return NextResponse.json({
      success: false,
      error: 'internal_error',
      message: 'Sunucu hatası oluştu'
    }, { status: 500 });
  }
}

/**
 * Verify if user has access to the salon
 */
async function verifySalonAccess(supabase: any, userId: string, salonId: string): Promise<boolean> {
  try {
    // Check if user is admin
    const { data: adminCheck } = await supabase.rpc('is_admin');
    if (adminCheck) {
      return true;
    }

    // Check if user is salon owner
    const { data: salon, error: salonError } = await supabase
      .from('salons')
      .select('owner_id')
      .eq('id', salonId)
      .single();

    if (salonError) {
      console.error('Error checking salon ownership:', salonError);
      return false;
    }

    if (salon?.owner_id === userId) {
      return true;
    }

    // Check if user is salon staff
    const { data: barber, error: barberError } = await supabase
      .from('barbers')
      .select('id')
      .eq('salon_id', salonId)
      .eq('user_id', userId)
      .single();

    if (barberError && barberError.code !== 'PGRST116') { // Not found error
      console.error('Error checking barber access:', barberError);
      return false;
    }

    return !!barber;

  } catch (error) {
    console.error('Error verifying salon access:', error);
    return false;
  }
}

/**
 * OPTIONS /api/sms/balance
 * Handle CORS preflight requests
 */
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
