/**
 * Public Appointment Cancellation Page
 * Allows customers to cancel appointments via secure tokens sent in SMS
 */

"use client"

import { useState, useEffect } from 'react'
import { useParams } from 'next/navigation'
import { motion } from 'framer-motion'
import { format } from 'date-fns'
import { tr } from 'date-fns/locale'
import { 
  Calendar, 
  Clock, 
  User, 
  Phone, 
  MapPin, 
  Scissors, 
  AlertTriangle,
  CheckCircle,
  XCircle,
  Loader2
} from 'lucide-react'

import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Separator } from '@/components/ui/separator'
import { Badge } from '@/components/ui/badge'
import { toast } from 'sonner'

interface AppointmentData {
  id: string
  date: string
  time: string
  endTime: string
  customerName: string
  customerPhone: string
  customerEmail?: string
  serviceName: string
  serviceDuration: number
  servicePrice: number
  barberName: string
  salonName: string
  salonPhone: string
  salonAddress: string
}

interface TokenData {
  id: string
  expiresAt: string
}

type PageState = 'loading' | 'confirm' | 'cancelling' | 'success' | 'error'

export default function CancelAppointmentPage() {
  const params = useParams()
  const token = params.token as string

  const [pageState, setPageState] = useState<PageState>('loading')
  const [appointment, setAppointment] = useState<AppointmentData | null>(null)
  const [tokenData, setTokenData] = useState<TokenData | null>(null)
  const [error, setError] = useState<string | null>(null)

  // Load appointment details on mount
  useEffect(() => {
    if (token) {
      loadAppointmentDetails()
    }
  }, [token])

  const loadAppointmentDetails = async () => {
    try {
      setPageState('loading')
      setError(null)

      const response = await fetch(`/api/sms/cancel/${token}`)
      const result = await response.json()

      if (result.success) {
        setAppointment(result.data.appointment)
        setTokenData(result.data.token)
        setPageState('confirm')
      } else {
        setError(result.message || 'Randevu bilgileri alınamadı')
        setPageState('error')
      }
    } catch (error) {
      console.error('Error loading appointment:', error)
      setError('Bağlantı hatası oluştu')
      setPageState('error')
    }
  }

  const handleCancelAppointment = async () => {
    if (!appointment || !token) return

    try {
      setPageState('cancelling')

      const response = await fetch(`/api/sms/cancel/${token}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          confirm: true,
          customerName: appointment.customerName,
          customerPhone: appointment.customerPhone
        }),
      })

      const result = await response.json()

      if (result.success) {
        setPageState('success')
        toast.success('Randevunuz başarıyla iptal edildi')
      } else {
        setError(result.message || 'Randevu iptal edilemedi')
        setPageState('error')
        toast.error(result.message || 'Randevu iptal edilemedi')
      }
    } catch (error) {
      console.error('Error cancelling appointment:', error)
      setError('İptal işlemi sırasında hata oluştu')
      setPageState('error')
      toast.error('İptal işlemi sırasında hata oluştu')
    }
  }

  const formatAppointmentDate = (dateStr: string) => {
    try {
      const date = new Date(dateStr)
      return format(date, 'dd MMMM yyyy, EEEE', { locale: tr })
    } catch {
      return dateStr
    }
  }

  const formatAppointmentTime = (timeStr: string) => {
    try {
      return timeStr.substring(0, 5) // HH:mm format
    } catch {
      return timeStr
    }
  }

  const isTokenExpired = () => {
    if (!tokenData) return false
    return new Date(tokenData.expiresAt) < new Date()
  }

  if (pageState === 'loading') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-background to-muted/20 flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardContent className="flex flex-col items-center justify-center py-12">
            <Loader2 className="h-8 w-8 animate-spin text-primary mb-4" />
            <p className="text-muted-foreground">Randevu bilgileri yükleniyor...</p>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (pageState === 'error') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-background to-muted/20 flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <div className="mx-auto w-12 h-12 bg-destructive/10 rounded-full flex items-center justify-center mb-4">
              <XCircle className="h-6 w-6 text-destructive" />
            </div>
            <CardTitle className="text-destructive">Hata Oluştu</CardTitle>
            <CardDescription>{error}</CardDescription>
          </CardHeader>
          <CardContent>
            <Button 
              onClick={loadAppointmentDetails} 
              className="w-full"
              variant="outline"
            >
              Tekrar Dene
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (pageState === 'success') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-background to-muted/20 flex items-center justify-center p-4">
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.3 }}
        >
          <Card className="w-full max-w-md">
            <CardHeader className="text-center">
              <div className="mx-auto w-12 h-12 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center mb-4">
                <CheckCircle className="h-6 w-6 text-green-600 dark:text-green-400" />
              </div>
              <CardTitle className="text-green-600 dark:text-green-400">Randevu İptal Edildi</CardTitle>
              <CardDescription>
                Randevunuz başarıyla iptal edildi. SMS ile onay mesajı gönderilecektir.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center text-sm text-muted-foreground">
                <p>Yeni randevu almak için salonu arayabilirsiniz:</p>
                {appointment && (
                  <p className="font-medium mt-1">{appointment.salonPhone}</p>
                )}
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    )
  }

  if (pageState === 'confirm' && appointment) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-background to-muted/20 flex items-center justify-center p-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
          className="w-full max-w-lg"
        >
          <Card>
            <CardHeader className="text-center">
              <div className="mx-auto w-12 h-12 bg-orange-100 dark:bg-orange-900/20 rounded-full flex items-center justify-center mb-4">
                <AlertTriangle className="h-6 w-6 text-orange-600 dark:text-orange-400" />
              </div>
              <CardTitle>Randevu İptali</CardTitle>
              <CardDescription>
                Aşağıdaki randevuyu iptal etmek istediğinizden emin misiniz?
              </CardDescription>
            </CardHeader>

            <CardContent className="space-y-6">
              {/* Appointment Details */}
              <div className="space-y-4">
                <div className="flex items-center space-x-3">
                  <MapPin className="h-5 w-5 text-muted-foreground" />
                  <div>
                    <p className="font-medium">{appointment.salonName}</p>
                    <p className="text-sm text-muted-foreground">{appointment.salonAddress}</p>
                  </div>
                </div>

                <Separator />

                <div className="grid grid-cols-2 gap-4">
                  <div className="flex items-center space-x-3">
                    <Calendar className="h-5 w-5 text-muted-foreground" />
                    <div>
                      <p className="text-sm text-muted-foreground">Tarih</p>
                      <p className="font-medium">{formatAppointmentDate(appointment.date)}</p>
                    </div>
                  </div>

                  <div className="flex items-center space-x-3">
                    <Clock className="h-5 w-5 text-muted-foreground" />
                    <div>
                      <p className="text-sm text-muted-foreground">Saat</p>
                      <p className="font-medium">
                        {formatAppointmentTime(appointment.time)} - {formatAppointmentTime(appointment.endTime)}
                      </p>
                    </div>
                  </div>
                </div>

                <Separator />

                <div className="space-y-3">
                  <div className="flex items-center space-x-3">
                    <Scissors className="h-5 w-5 text-muted-foreground" />
                    <div>
                      <p className="text-sm text-muted-foreground">Hizmet</p>
                      <p className="font-medium">{appointment.serviceName}</p>
                      <p className="text-sm text-muted-foreground">
                        {appointment.serviceDuration} dakika • ₺{appointment.servicePrice}
                      </p>
                    </div>
                  </div>

                  <div className="flex items-center space-x-3">
                    <User className="h-5 w-5 text-muted-foreground" />
                    <div>
                      <p className="text-sm text-muted-foreground">Berber</p>
                      <p className="font-medium">{appointment.barberName}</p>
                    </div>
                  </div>

                  <div className="flex items-center space-x-3">
                    <Phone className="h-5 w-5 text-muted-foreground" />
                    <div>
                      <p className="text-sm text-muted-foreground">Müşteri</p>
                      <p className="font-medium">{appointment.customerName}</p>
                      <p className="text-sm text-muted-foreground">{appointment.customerPhone}</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Token Expiry Warning */}
              {isTokenExpired() && (
                <div className="bg-destructive/10 border border-destructive/20 rounded-lg p-3">
                  <p className="text-sm text-destructive font-medium">
                    Bu iptal bağlantısının süresi dolmuş. Randevuyu iptal etmek için salonu arayın.
                  </p>
                </div>
              )}

              {/* Action Buttons */}
              <div className="flex space-x-3">
                <Button
                  variant="outline"
                  className="flex-1"
                  onClick={() => window.close()}
                >
                  Vazgeç
                </Button>
                <Button
                  variant="destructive"
                  className="flex-1"
                  onClick={handleCancelAppointment}
                  disabled={isTokenExpired()}
                >
                  Randevuyu İptal Et
                </Button>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    )
  }

  if (pageState === 'cancelling') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-background to-muted/20 flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardContent className="flex flex-col items-center justify-center py-12">
            <Loader2 className="h-8 w-8 animate-spin text-primary mb-4" />
            <p className="text-muted-foreground">Randevu iptal ediliyor...</p>
          </CardContent>
        </Card>
      </div>
    )
  }

  return null
}
